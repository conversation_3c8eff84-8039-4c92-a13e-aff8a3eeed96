{"version": 3, "sources": [], "sections": [{"offset": {"line": 3, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/app/chat.module.css [app-client] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"appContainer\": \"chat-module__Ix5oLa__appContainer\",\n  \"assistantMessage\": \"chat-module__Ix5oLa__assistantMessage\",\n  \"chatContainer\": \"chat-module__Ix5oLa__chatContainer\",\n  \"chatMain\": \"chat-module__Ix5oLa__chatMain\",\n  \"inputArea\": \"chat-module__Ix5oLa__inputArea\",\n  \"inputBox\": \"chat-module__Ix5oLa__inputBox\",\n  \"mainContent\": \"chat-module__Ix5oLa__mainContent\",\n  \"messageContent\": \"chat-module__Ix5oLa__messageContent\",\n  \"messages\": \"chat-module__Ix5oLa__messages\",\n  \"modelSelectorContainer\": \"chat-module__Ix5oLa__modelSelectorContainer\",\n  \"placeholderContent\": \"chat-module__Ix5oLa__placeholderContent\",\n  \"sendButton\": \"chat-module__Ix5oLa__sendButton\",\n  \"userMessage\": \"chat-module__Ix5oLa__userMessage\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA"}}, {"offset": {"line": 22, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/AI/MetaRAG/metarag/frontend/src/app/components/TypeWriter.tsx"], "sourcesContent": ["\"use client\";\nimport React, { useState, useEffect, useCallback } from 'react';\n\ninterface TypeWriterProps {\n  text: string;\n  speed?: number; // 基础打字速度（毫秒）\n  className?: string;\n  onComplete?: () => void;\n  allowSkip?: boolean; // 是否允许点击跳过\n}\n\nexport default function TypeWriter({\n  text,\n  speed = 30,\n  className = '',\n  onComplete,\n  allowSkip = true\n}: TypeWriterProps) {\n  const [displayedText, setDisplayedText] = useState('');\n  const [currentIndex, setCurrentIndex] = useState(0);\n  const [isSkipped, setIsSkipped] = useState(false);\n\n  useEffect(() => {\n    // 重置状态当文本改变时\n    setDisplayedText('');\n    setCurrentIndex(0);\n    setIsSkipped(false);\n  }, [text]);\n\n  const handleSkip = useCallback(() => {\n    if (allowSkip && currentIndex < text.length) {\n      setIsSkipped(true);\n      setDisplayedText(text);\n      setCurrentIndex(text.length);\n    }\n  }, [allowSkip, currentIndex, text]);\n\n  const getCharDelay = useCallback((char: string, index: number) => {\n    // 为不同字符类型设置不同的延迟，使打字更自然\n    if (char === ' ') return speed * 0.5; // 空格快一些\n    if (char === '，' || char === '。' || char === '！' || char === '？') return speed * 2; // 标点符号慢一些\n    if (char === '\\n') return speed * 1.5; // 换行稍慢\n\n    // 添加轻微的随机变化使打字更自然\n    const randomFactor = 0.8 + Math.random() * 0.4; // 0.8 到 1.2 的随机因子\n    return speed * randomFactor;\n  }, [speed]);\n\n  useEffect(() => {\n    if (isSkipped) {\n      // 如果被跳过，立即完成\n      if (onComplete) {\n        const timer = setTimeout(() => {\n          onComplete();\n        }, 100);\n        return () => clearTimeout(timer);\n      }\n      return;\n    }\n\n    if (currentIndex < text.length) {\n      const char = text[currentIndex];\n      const delay = getCharDelay(char, currentIndex);\n\n      const timer = setTimeout(() => {\n        setDisplayedText(prev => prev + char);\n        setCurrentIndex(prev => prev + 1);\n      }, delay);\n\n      return () => clearTimeout(timer);\n    } else if (currentIndex === text.length && onComplete) {\n      // 打字完成后稍等一下再调用回调，让用户看到完整文本\n      const timer = setTimeout(() => {\n        onComplete();\n      }, 500);\n      return () => clearTimeout(timer);\n    }\n  }, [currentIndex, text, getCharDelay, onComplete, isSkipped]);\n\n  return (\n    <span\n      className={className}\n      onClick={handleSkip}\n      style={{\n        cursor: allowSkip && currentIndex < text.length ? 'pointer' : 'default',\n        userSelect: 'text'\n      }}\n      title={allowSkip && currentIndex < text.length ? '点击跳过打字动画' : undefined}\n    >\n      {displayedText}\n      {currentIndex < text.length && !isSkipped && (\n        <span className=\"typewriter-cursor\">|</span>\n      )}\n    </span>\n  );\n}\n"], "names": [], "mappings": ";;;;;AACA;;;AADA;;AAWe,SAAS,WAAW,KAMjB;QANiB,EACjC,IAAI,EACJ,QAAQ,EAAE,EACV,YAAY,EAAE,EACd,UAAU,EACV,YAAY,IAAI,EACA,GANiB;;IAOjC,MAAM,CAAC,eAAe,iBAAiB,GAAG,IAAA,yKAAQ,EAAC;IACnD,MAAM,CAAC,cAAc,gBAAgB,GAAG,IAAA,yKAAQ,EAAC;IACjD,MAAM,CAAC,WAAW,aAAa,GAAG,IAAA,yKAAQ,EAAC;IAE3C,IAAA,0KAAS;gCAAC;YACR,aAAa;YACb,iBAAiB;YACjB,gBAAgB;YAChB,aAAa;QACf;+BAAG;QAAC;KAAK;IAET,MAAM,aAAa,IAAA,4KAAW;8CAAC;YAC7B,IAAI,aAAa,eAAe,KAAK,MAAM,EAAE;gBAC3C,aAAa;gBACb,iBAAiB;gBACjB,gBAAgB,KAAK,MAAM;YAC7B;QACF;6CAAG;QAAC;QAAW;QAAc;KAAK;IAElC,MAAM,eAAe,IAAA,4KAAW;gDAAC,CAAC,MAAc;YAC9C,wBAAwB;YACxB,IAAI,SAAS,KAAK,OAAO,QAAQ,KAAK,QAAQ;YAC9C,IAAI,SAAS,OAAO,SAAS,OAAO,SAAS,OAAO,SAAS,KAAK,OAAO,QAAQ,GAAG,UAAU;YAC9F,IAAI,SAAS,MAAM,OAAO,QAAQ,KAAK,OAAO;YAE9C,kBAAkB;YAClB,MAAM,eAAe,MAAM,KAAK,MAAM,KAAK,KAAK,kBAAkB;YAClE,OAAO,QAAQ;QACjB;+CAAG;QAAC;KAAM;IAEV,IAAA,0KAAS;gCAAC;YACR,IAAI,WAAW;gBACb,aAAa;gBACb,IAAI,YAAY;oBACd,MAAM,QAAQ;sDAAW;4BACvB;wBACF;qDAAG;oBACH;gDAAO,IAAM,aAAa;;gBAC5B;gBACA;YACF;YAEA,IAAI,eAAe,KAAK,MAAM,EAAE;gBAC9B,MAAM,OAAO,IAAI,CAAC,aAAa;gBAC/B,MAAM,QAAQ,aAAa,MAAM;gBAEjC,MAAM,QAAQ;kDAAW;wBACvB;0DAAiB,CAAA,OAAQ,OAAO;;wBAChC;0DAAgB,CAAA,OAAQ,OAAO;;oBACjC;iDAAG;gBAEH;4CAAO,IAAM,aAAa;;YAC5B,OAAO,IAAI,iBAAiB,KAAK,MAAM,IAAI,YAAY;gBACrD,2BAA2B;gBAC3B,MAAM,QAAQ;kDAAW;wBACvB;oBACF;iDAAG;gBACH;4CAAO,IAAM,aAAa;;YAC5B;QACF;+BAAG;QAAC;QAAc;QAAM;QAAc;QAAY;KAAU;IAE5D,qBACE,6LAAC;QACC,WAAW;QACX,SAAS;QACT,OAAO;YACL,QAAQ,aAAa,eAAe,KAAK,MAAM,GAAG,YAAY;YAC9D,YAAY;QACd;QACA,OAAO,aAAa,eAAe,KAAK,MAAM,GAAG,aAAa;;YAE7D;YACA,eAAe,KAAK,MAAM,IAAI,CAAC,2BAC9B,6LAAC;gBAAK,WAAU;0BAAoB;;;;;;;;;;;;AAI5C;GApFwB;KAAA", "debugId": null}}, {"offset": {"line": 160, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/app/components/Sidebar.module.css [app-client] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"active\": \"Sidebar-module__JvW8nW__active\",\n  \"icon\": \"Sidebar-module__JvW8nW__icon\",\n  \"menuItem\": \"Sidebar-module__JvW8nW__menuItem\",\n  \"menuItems\": \"Sidebar-module__JvW8nW__menuItems\",\n  \"sidebar\": \"Sidebar-module__JvW8nW__sidebar\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA"}}, {"offset": {"line": 171, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/AI/MetaRAG/metarag/frontend/src/app/components/Sidebar.tsx"], "sourcesContent": ["\"use client\";\nimport React, { useState } from 'react';\nimport styles from './Sidebar.module.css';\n\ninterface SidebarProps {\n  activeTab: string;\n  onTabChange: (tab: string) => void;\n}\n\nconst Sidebar: React.FC<SidebarProps> = ({ activeTab, onTabChange }) => {\n  const menuItems = [\n    {\n      id: 'chat',\n      icon: (\n        <svg width=\"18\" height=\"18\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\n          <path d=\"M20 2H4c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h4l4 4 4-4h4c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zm-2 12H6v-2h12v2zm0-3H6V9h12v2zm0-3H6V6h12v2z\"/>\n        </svg>\n      ),\n      title: '对话历史',\n      tooltip: '查看对话历史'\n    },\n    {\n      id: 'code-review',\n      icon: (\n        <svg width=\"18\" height=\"18\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\n          <path d=\"M9.4 16.6L4.8 12l4.6-4.6L8 6l-6 6 6 6 1.4-1.4zm5.2 0L19.2 12l-4.6-4.6L16 6l6 6-6 6-1.4-1.4z\"/>\n        </svg>\n      ),\n      title: 'Code Review Assistant',\n      tooltip: '代码审查助手'\n    },\n    {\n      id: 'db-assistant',\n      icon: (\n        <svg width=\"18\" height=\"18\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\n          <path d=\"M12 3C7.58 3 4 4.79 4 7s3.58 4 8 4 8-1.79 8-4-3.58-4-8-4zM4 9v3c0 2.21 3.58 4 8 4s8-1.79 8-4V9c0 2.21-3.58 4-8 4s-8-1.79-8-4zm0 5v3c0 2.21 3.58 4 8 4s8-1.79 8-4v-3c0 2.21-3.58 4-8 4s-8-1.79-8-4z\"/>\n        </svg>\n      ),\n      title: 'DB Assistant',\n      tooltip: '数据库助手'\n    },\n    {\n      id: 'settings',\n      icon: (\n        <svg width=\"18\" height=\"18\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\n          <path d=\"M19.14,12.94c0.04-0.3,0.06-0.61,0.06-0.94c0-0.32-0.02-0.64-0.07-0.94l2.03-1.58c0.18-0.14,0.23-0.41,0.12-0.61 l-1.92-3.32c-0.12-0.22-0.37-0.29-0.59-0.22l-2.39,0.96c-0.5-0.38-1.03-0.7-1.62-0.94L14.4,2.81c-0.04-0.24-0.24-0.41-0.48-0.41 h-3.84c-0.24,0-0.43,0.17-0.47,0.41L9.25,5.35C8.66,5.59,8.12,5.92,7.63,6.29L5.24,5.33c-0.22-0.08-0.47,0-0.59,0.22L2.74,8.87 C2.62,9.08,2.66,9.34,2.86,9.48l2.03,1.58C4.84,11.36,4.8,11.69,4.8,12s0.02,0.64,0.07,0.94l-2.03,1.58 c-0.18,0.14-0.23,0.41-0.12,0.61l1.92,3.32c0.12,0.22,0.37,0.29,0.59,0.22l2.39-0.96c0.5,0.38,1.03,0.7,1.62,0.94l0.36,2.54 c0.05,0.24,0.24,0.41,0.48,0.41h3.84c0.24,0,0.44-0.17,0.47-0.41l0.36-2.54c0.59-0.24,1.13-0.56,1.62-0.94l2.39,0.96 c0.22,0.08,0.47,0,0.59-0.22l1.92-3.32c0.12-0.22,0.07-0.47-0.12-0.61L19.14,12.94z M12,15.6c-1.98,0-3.6-1.62-3.6-3.6 s1.62-3.6,3.6-3.6s3.6,1.62,3.6,3.6S13.98,15.6,12,15.6z\"/>\n        </svg>\n      ),\n      title: '设置',\n      tooltip: '系统设置'\n    }\n  ];\n\n  return (\n    <div className={styles.sidebar}>\n      <div className={styles.menuItems}>\n        {menuItems.map((item) => (\n          <button\n            key={item.id}\n            className={`${styles.menuItem} ${activeTab === item.id ? styles.active : ''}`}\n            onClick={() => onTabChange(item.id)}\n            title={item.tooltip}\n          >\n            <div className={styles.icon}>\n              {item.icon}\n            </div>\n          </button>\n        ))}\n      </div>\n    </div>\n  );\n};\n\nexport default Sidebar;\n"], "names": [], "mappings": ";;;;;AAEA;AAFA;;;AASA,MAAM,UAAkC;QAAC,EAAE,SAAS,EAAE,WAAW,EAAE;IACjE,MAAM,YAAY;QAChB;YACE,IAAI;YACJ,oBACE,6LAAC;gBAAI,OAAM;gBAAK,QAAO;gBAAK,SAAQ;gBAAY,MAAK;0BACnD,cAAA,6LAAC;oBAAK,GAAE;;;;;;;;;;;YAGZ,OAAO;YACP,SAAS;QACX;QACA;YACE,IAAI;YACJ,oBACE,6LAAC;gBAAI,OAAM;gBAAK,QAAO;gBAAK,SAAQ;gBAAY,MAAK;0BACnD,cAAA,6LAAC;oBAAK,GAAE;;;;;;;;;;;YAGZ,OAAO;YACP,SAAS;QACX;QACA;YACE,IAAI;YACJ,oBACE,6LAAC;gBAAI,OAAM;gBAAK,QAAO;gBAAK,SAAQ;gBAAY,MAAK;0BACnD,cAAA,6LAAC;oBAAK,GAAE;;;;;;;;;;;YAGZ,OAAO;YACP,SAAS;QACX;QACA;YACE,IAAI;YACJ,oBACE,6LAAC;gBAAI,OAAM;gBAAK,QAAO;gBAAK,SAAQ;gBAAY,MAAK;0BACnD,cAAA,6LAAC;oBAAK,GAAE;;;;;;;;;;;YAGZ,OAAO;YACP,SAAS;QACX;KACD;IAED,qBACE,6LAAC;QAAI,WAAW,6JAAM,CAAC,OAAO;kBAC5B,cAAA,6LAAC;YAAI,WAAW,6JAAM,CAAC,SAAS;sBAC7B,UAAU,GAAG,CAAC,CAAC,qBACd,6LAAC;oBAEC,WAAW,AAAC,GAAqB,OAAnB,6JAAM,CAAC,QAAQ,EAAC,KAA8C,OAA3C,cAAc,KAAK,EAAE,GAAG,6JAAM,CAAC,MAAM,GAAG;oBACzE,SAAS,IAAM,YAAY,KAAK,EAAE;oBAClC,OAAO,KAAK,OAAO;8BAEnB,cAAA,6LAAC;wBAAI,WAAW,6JAAM,CAAC,IAAI;kCACxB,KAAK,IAAI;;;;;;mBANP,KAAK,EAAE;;;;;;;;;;;;;;;AAaxB;KA9DM;uCAgES", "debugId": null}}, {"offset": {"line": 314, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/app/components/ModelSelector.module.css [app-client] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"chevron\": \"ModelSelector-module__0aG-aG__chevron\",\n  \"chevronUp\": \"ModelSelector-module__0aG-aG__chevronUp\",\n  \"dropdown\": \"ModelSelector-module__0aG-aG__dropdown\",\n  \"dropdownHeader\": \"ModelSelector-module__0aG-aG__dropdownHeader\",\n  \"modelItem\": \"ModelSelector-module__0aG-aG__modelItem\",\n  \"modelItemName\": \"ModelSelector-module__0aG-aG__modelItemName\",\n  \"modelItemTooltip\": \"ModelSelector-module__0aG-aG__modelItemTooltip\",\n  \"modelItemTooltipText\": \"ModelSelector-module__0aG-aG__modelItemTooltipText\",\n  \"modelList\": \"ModelSelector-module__0aG-aG__modelList\",\n  \"modelName\": \"ModelSelector-module__0aG-aG__modelName\",\n  \"modelSelector\": \"ModelSelector-module__0aG-aG__modelSelector\",\n  \"selected\": \"ModelSelector-module__0aG-aG__selected\",\n  \"selectorButton\": \"ModelSelector-module__0aG-aG__selectorButton\",\n  \"tooltipArrow\": \"ModelSelector-module__0aG-aG__tooltipArrow\",\n  \"visible\": \"ModelSelector-module__0aG-aG__visible\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA"}}, {"offset": {"line": 335, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/AI/MetaRAG/metarag/frontend/src/app/components/ModelSelector.tsx"], "sourcesContent": ["\"use client\";\nimport React, { useState, useEffect, useRef } from 'react';\nimport styles from './ModelSelector.module.css';\n\ninterface ModelSelectorProps {\n  selectedModel: string;\n  onModelChange: (model: string) => void;\n}\n\n\n\nconst ModelSelector: React.FC<ModelSelectorProps> = ({ selectedModel, onModelChange }) => {\n  const [models, setModels] = useState<string[]>([]);\n  const [isOpen, setIsOpen] = useState(false);\n  const [loading, setLoading] = useState(true);\n  const [hoveredModel, setHoveredModel] = useState<string | null>(null);\n  const [tooltipPosition, setTooltipPosition] = useState({ x: 0, y: 0 });\n  const dropdownRef = useRef<HTMLDivElement>(null);\n\n  // 将模型名称首字母大写\n  const capitalizeModelName = (name: string) => {\n    return name.charAt(0).toUpperCase() + name.slice(1);\n  };\n\n  // 处理鼠标进入事件，计算tooltip位置\n  const handleMouseEnter = (model: string, event: React.MouseEvent) => {\n    const rect = event.currentTarget.getBoundingClientRect();\n    setTooltipPosition({\n      x: rect.right + 10, // 显示在右侧\n      y: rect.top + rect.height / 2\n    });\n    setHoveredModel(model);\n  };\n\n  // 处理鼠标离开事件\n  const handleMouseLeave = () => {\n    setHoveredModel(null);\n  };\n\n  useEffect(() => {\n    fetchModels();\n  }, []);\n\n  useEffect(() => {\n    const handleClickOutside = (event: MouseEvent) => {\n      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {\n        setIsOpen(false);\n      }\n    };\n\n    document.addEventListener('mousedown', handleClickOutside);\n    return () => {\n      document.removeEventListener('mousedown', handleClickOutside);\n    };\n  }, []);\n\n  const fetchModels = async () => {\n    try {\n      setLoading(true);\n      const response = await fetch('/api/models');\n      if (response.ok) {\n        const data = await response.json();\n        // 过滤掉含有\"embed\"的模型，并按字母顺序排列\n        const filteredModels = (data.models || [])\n          .filter((model: string) => !model.toLowerCase().includes('embed'))\n          .sort((a: string, b: string) => a.localeCompare(b));\n        setModels(filteredModels);\n      } else {\n        // 如果API不可用，使用默认模型\n        setModels(['qwen3:8b']);\n      }\n    } catch (error) {\n      console.error('Failed to fetch models:', error);\n      // 如果获取失败，使用默认模型\n      setModels(['qwen3:8b']);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleModelSelect = (model: string) => {\n    onModelChange(model);\n    setIsOpen(false);\n  };\n\n  const displayName = selectedModel || 'qwen3:8b';\n\n  return (\n    <div className={styles.modelSelector} ref={dropdownRef}>\n      <button\n        className={styles.selectorButton}\n        onClick={() => setIsOpen(!isOpen)}\n        disabled={loading}\n      >\n        <span className={styles.modelName}>\n          {loading ? '加载中...' : capitalizeModelName(displayName)}\n        </span>\n        <svg\n          className={`${styles.chevron} ${isOpen ? styles.chevronUp : ''}`}\n          width=\"16\"\n          height=\"16\"\n          viewBox=\"0 0 24 24\"\n          fill=\"currentColor\"\n        >\n          <path d=\"M7 10l5 5 5-5z\"/>\n        </svg>\n      </button>\n\n      {isOpen && !loading && (\n        <div className={styles.dropdown}>\n          <div className={styles.dropdownHeader}>\n            <span>选择AI模型</span>\n          </div>\n          <div className={styles.modelList}>\n            {models.map((model) => (\n              <div\n                key={model}\n                className={styles.modelItemTooltip}\n                onMouseEnter={(e) => handleMouseEnter(model, e)}\n                onMouseLeave={handleMouseLeave}\n              >\n                <button\n                  className={`${styles.modelItem} ${selectedModel === model ? styles.selected : ''}`}\n                  onClick={() => handleModelSelect(model)}\n                >\n                  <span className={styles.modelItemName}>{capitalizeModelName(model)}</span>\n                  {selectedModel === model && (\n                    <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\n                      <path d=\"M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z\"/>\n                    </svg>\n                  )}\n                </button>\n              </div>\n            ))}\n          </div>\n\n          {/* 全局tooltip */}\n          {hoveredModel && (\n            <div\n              className={`${styles.modelItemTooltipText} ${styles.visible}`}\n              style={{\n                left: `${tooltipPosition.x}px`,\n                top: `${tooltipPosition.y}px`,\n                transform: 'translate(0, -50%)'\n              }}\n            >\n              {capitalizeModelName(hoveredModel)}\n              <div className={styles.tooltipArrow}></div>\n            </div>\n          )}\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default ModelSelector;\n"], "names": [], "mappings": ";;;;;AACA;AACA;;;AAFA;;;AAWA,MAAM,gBAA8C;QAAC,EAAE,aAAa,EAAE,aAAa,EAAE;;IACnF,MAAM,CAAC,QAAQ,UAAU,GAAG,IAAA,yKAAQ,EAAW,EAAE;IACjD,MAAM,CAAC,QAAQ,UAAU,GAAG,IAAA,yKAAQ,EAAC;IACrC,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,yKAAQ,EAAC;IACvC,MAAM,CAAC,cAAc,gBAAgB,GAAG,IAAA,yKAAQ,EAAgB;IAChE,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,IAAA,yKAAQ,EAAC;QAAE,GAAG;QAAG,GAAG;IAAE;IACpE,MAAM,cAAc,IAAA,uKAAM,EAAiB;IAE3C,aAAa;IACb,MAAM,sBAAsB,CAAC;QAC3B,OAAO,KAAK,MAAM,CAAC,GAAG,WAAW,KAAK,KAAK,KAAK,CAAC;IACnD;IAEA,uBAAuB;IACvB,MAAM,mBAAmB,CAAC,OAAe;QACvC,MAAM,OAAO,MAAM,aAAa,CAAC,qBAAqB;QACtD,mBAAmB;YACjB,GAAG,KAAK,KAAK,GAAG;YAChB,GAAG,KAAK,GAAG,GAAG,KAAK,MAAM,GAAG;QAC9B;QACA,gBAAgB;IAClB;IAEA,WAAW;IACX,MAAM,mBAAmB;QACvB,gBAAgB;IAClB;IAEA,IAAA,0KAAS;mCAAC;YACR;QACF;kCAAG,EAAE;IAEL,IAAA,0KAAS;mCAAC;YACR,MAAM;8DAAqB,CAAC;oBAC1B,IAAI,YAAY,OAAO,IAAI,CAAC,YAAY,OAAO,CAAC,QAAQ,CAAC,MAAM,MAAM,GAAW;wBAC9E,UAAU;oBACZ;gBACF;;YAEA,SAAS,gBAAgB,CAAC,aAAa;YACvC;2CAAO;oBACL,SAAS,mBAAmB,CAAC,aAAa;gBAC5C;;QACF;kCAAG,EAAE;IAEL,MAAM,cAAc;QAClB,IAAI;YACF,WAAW;YACX,MAAM,WAAW,MAAM,MAAM;YAC7B,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,2BAA2B;gBAC3B,MAAM,iBAAiB,CAAC,KAAK,MAAM,IAAI,EAAE,EACtC,MAAM,CAAC,CAAC,QAAkB,CAAC,MAAM,WAAW,GAAG,QAAQ,CAAC,UACxD,IAAI,CAAC,CAAC,GAAW,IAAc,EAAE,aAAa,CAAC;gBAClD,UAAU;YACZ,OAAO;gBACL,kBAAkB;gBAClB,UAAU;oBAAC;iBAAW;YACxB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;YACzC,gBAAgB;YAChB,UAAU;gBAAC;aAAW;QACxB,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,oBAAoB,CAAC;QACzB,cAAc;QACd,UAAU;IACZ;IAEA,MAAM,cAAc,iBAAiB;IAErC,qBACE,6LAAC;QAAI,WAAW,mKAAM,CAAC,aAAa;QAAE,KAAK;;0BACzC,6LAAC;gBACC,WAAW,mKAAM,CAAC,cAAc;gBAChC,SAAS,IAAM,UAAU,CAAC;gBAC1B,UAAU;;kCAEV,6LAAC;wBAAK,WAAW,mKAAM,CAAC,SAAS;kCAC9B,UAAU,WAAW,oBAAoB;;;;;;kCAE5C,6LAAC;wBACC,WAAW,AAAC,GAAoB,OAAlB,mKAAM,CAAC,OAAO,EAAC,KAAkC,OAA/B,SAAS,mKAAM,CAAC,SAAS,GAAG;wBAC5D,OAAM;wBACN,QAAO;wBACP,SAAQ;wBACR,MAAK;kCAEL,cAAA,6LAAC;4BAAK,GAAE;;;;;;;;;;;;;;;;;YAIX,UAAU,CAAC,yBACV,6LAAC;gBAAI,WAAW,mKAAM,CAAC,QAAQ;;kCAC7B,6LAAC;wBAAI,WAAW,mKAAM,CAAC,cAAc;kCACnC,cAAA,6LAAC;sCAAK;;;;;;;;;;;kCAER,6LAAC;wBAAI,WAAW,mKAAM,CAAC,SAAS;kCAC7B,OAAO,GAAG,CAAC,CAAC,sBACX,6LAAC;gCAEC,WAAW,mKAAM,CAAC,gBAAgB;gCAClC,cAAc,CAAC,IAAM,iBAAiB,OAAO;gCAC7C,cAAc;0CAEd,cAAA,6LAAC;oCACC,WAAW,AAAC,GAAsB,OAApB,mKAAM,CAAC,SAAS,EAAC,KAAkD,OAA/C,kBAAkB,QAAQ,mKAAM,CAAC,QAAQ,GAAG;oCAC9E,SAAS,IAAM,kBAAkB;;sDAEjC,6LAAC;4CAAK,WAAW,mKAAM,CAAC,aAAa;sDAAG,oBAAoB;;;;;;wCAC3D,kBAAkB,uBACjB,6LAAC;4CAAI,OAAM;4CAAK,QAAO;4CAAK,SAAQ;4CAAY,MAAK;sDACnD,cAAA,6LAAC;gDAAK,GAAE;;;;;;;;;;;;;;;;;+BAZT;;;;;;;;;;oBAqBV,8BACC,6LAAC;wBACC,WAAW,AAAC,GAAiC,OAA/B,mKAAM,CAAC,oBAAoB,EAAC,KAAkB,OAAf,mKAAM,CAAC,OAAO;wBAC3D,OAAO;4BACL,MAAM,AAAC,GAAoB,OAAlB,gBAAgB,CAAC,EAAC;4BAC3B,KAAK,AAAC,GAAoB,OAAlB,gBAAgB,CAAC,EAAC;4BAC1B,WAAW;wBACb;;4BAEC,oBAAoB;0CACrB,6LAAC;gCAAI,WAAW,mKAAM,CAAC,YAAY;;;;;;;;;;;;;;;;;;;;;;;;AAOjD;GA/IM;KAAA;uCAiJS", "debugId": null}}, {"offset": {"line": 583, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/app/components/DocumentsSection.module.css [app-client] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"documentContent\": \"DocumentsSection-module__2JF54W__documentContent\",\n  \"documentItem\": \"DocumentsSection-module__2JF54W__documentItem\",\n  \"documentMetadata\": \"DocumentsSection-module__2JF54W__documentMetadata\",\n  \"documentTitle\": \"DocumentsSection-module__2JF54W__documentTitle\",\n  \"documentsContent\": \"DocumentsSection-module__2JF54W__documentsContent\",\n  \"documentsHeader\": \"DocumentsSection-module__2JF54W__documentsHeader\",\n  \"documentsSection\": \"DocumentsSection-module__2JF54W__documentsSection\",\n  \"documentsTitle\": \"DocumentsSection-module__2JF54W__documentsTitle\",\n  \"expandIcon\": \"DocumentsSection-module__2JF54W__expandIcon\",\n  \"expanded\": \"DocumentsSection-module__2JF54W__expanded\",\n  \"noDocuments\": \"DocumentsSection-module__2JF54W__noDocuments\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA"}}, {"offset": {"line": 600, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/AI/MetaRAG/metarag/frontend/src/app/components/DocumentsSection.tsx"], "sourcesContent": ["\"use client\";\nimport React, { useState } from 'react';\nimport styles from './DocumentsSection.module.css';\n\ninterface Document {\n  title: string;\n  page_content: string;\n  metadata: Record<string, any>;\n}\n\ninterface DocumentsSectionProps {\n  documents: Document[];\n}\n\nconst DocumentsSection: React.FC<DocumentsSectionProps> = ({ documents }) => {\n  const [isExpanded, setIsExpanded] = useState(false);\n\n  if (!documents || documents.length === 0) {\n    return (\n      <div className={styles.documentsSection}>\n        <div className={styles.noDocuments}>\n          This answer is based on the AI's own knowledge. No relevant documents in the knowledge base.\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className={styles.documentsSection}>\n      <div \n        className={styles.documentsHeader}\n        onClick={() => setIsExpanded(!isExpanded)}\n      >\n        <span className={styles.documentsTitle}>\n          📚 Related Documents ({documents.length})\n        </span>\n        <span className={`${styles.expandIcon} ${isExpanded ? styles.expanded : ''}`}>\n          ▼\n        </span>\n      </div>\n      \n      {isExpanded && (\n        <div className={styles.documentsContent}>\n          {documents.map((doc, index) => (\n            <div key={index} className={styles.documentItem}>\n              <div className={styles.documentTitle}>\n                {doc.title || `Document ${index + 1}`}\n              </div>\n              <div className={styles.documentContent}>\n                {doc.page_content}\n              </div>\n              {doc.metadata && Object.keys(doc.metadata).length > 0 && (\n                <div className={styles.documentMetadata}>\n                  <strong>Metadata:</strong>\n                  <pre>{JSON.stringify(doc.metadata, null, 2)}</pre>\n                </div>\n              )}\n            </div>\n          ))}\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default DocumentsSection;\n"], "names": [], "mappings": ";;;;;AACA;AACA;;;AAFA;;;AAcA,MAAM,mBAAoD;QAAC,EAAE,SAAS,EAAE;;IACtE,MAAM,CAAC,YAAY,cAAc,GAAG,IAAA,yKAAQ,EAAC;IAE7C,IAAI,CAAC,aAAa,UAAU,MAAM,KAAK,GAAG;QACxC,qBACE,6LAAC;YAAI,WAAW,sKAAM,CAAC,gBAAgB;sBACrC,cAAA,6LAAC;gBAAI,WAAW,sKAAM,CAAC,WAAW;0BAAE;;;;;;;;;;;IAK1C;IAEA,qBACE,6LAAC;QAAI,WAAW,sKAAM,CAAC,gBAAgB;;0BACrC,6LAAC;gBACC,WAAW,sKAAM,CAAC,eAAe;gBACjC,SAAS,IAAM,cAAc,CAAC;;kCAE9B,6LAAC;wBAAK,WAAW,sKAAM,CAAC,cAAc;;4BAAE;4BACf,UAAU,MAAM;4BAAC;;;;;;;kCAE1C,6LAAC;wBAAK,WAAW,AAAC,GAAuB,OAArB,sKAAM,CAAC,UAAU,EAAC,KAAqC,OAAlC,aAAa,sKAAM,CAAC,QAAQ,GAAG;kCAAM;;;;;;;;;;;;YAK/E,4BACC,6LAAC;gBAAI,WAAW,sKAAM,CAAC,gBAAgB;0BACpC,UAAU,GAAG,CAAC,CAAC,KAAK,sBACnB,6LAAC;wBAAgB,WAAW,sKAAM,CAAC,YAAY;;0CAC7C,6LAAC;gCAAI,WAAW,sKAAM,CAAC,aAAa;0CACjC,IAAI,KAAK,IAAI,AAAC,YAAqB,OAAV,QAAQ;;;;;;0CAEpC,6LAAC;gCAAI,WAAW,sKAAM,CAAC,eAAe;0CACnC,IAAI,YAAY;;;;;;4BAElB,IAAI,QAAQ,IAAI,OAAO,IAAI,CAAC,IAAI,QAAQ,EAAE,MAAM,GAAG,mBAClD,6LAAC;gCAAI,WAAW,sKAAM,CAAC,gBAAgB;;kDACrC,6LAAC;kDAAO;;;;;;kDACR,6LAAC;kDAAK,KAAK,SAAS,CAAC,IAAI,QAAQ,EAAE,MAAM;;;;;;;;;;;;;uBAVrC;;;;;;;;;;;;;;;;AAmBtB;GAjDM;KAAA;uCAmDS", "debugId": null}}, {"offset": {"line": 740, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/AI/MetaRAG/metarag/frontend/src/app/ChatPage.tsx"], "sourcesContent": ["\"use client\";\r\nimport React, { useState, useRef, useEffect } from 'react';\r\nimport styles from './chat.module.css';\r\nimport TypeWriter from './components/TypeWriter';\r\nimport Sidebar from './components/Sidebar';\r\nimport ModelSelector from './components/ModelSelector';\r\nimport DocumentsSection from './components/DocumentsSection';\r\n\r\ninterface Document {\r\n  title: string;\r\n  page_content: string;\r\n  metadata: Record<string, any>;\r\n}\r\n\r\ninterface Message {\r\n  role: 'user' | 'assistant';\r\n  content: string;\r\n  id: string;\r\n  isTyping?: boolean;\r\n  documents?: Document[];\r\n}\r\n\r\nconst initialMessages: Message[] = [\r\n  {\r\n    role: 'assistant',\r\n    content: '你好！我是AI助手，有什么可以帮您？',\r\n    id: 'initial-1',\r\n    isTyping: true\r\n  },\r\n];\r\n\r\nexport default function ChatPage() {\r\n  const [messages, setMessages] = useState<Message[]>(initialMessages);\r\n  const [input, setInput] = useState('');\r\n  const [activeTab, setActiveTab] = useState('chat');\r\n  const [selectedModel, setSelectedModel] = useState('qwen3:8b');\r\n  const messagesEndRef = useRef<HTMLDivElement>(null);\r\n\r\n  useEffect(() => {\r\n    const fetchCurrentModel = async () => {\r\n      try {\r\n        const response = await fetch('/api/current-model');\r\n        const data = await response.json();\r\n        if (data.current_model) {\r\n          setSelectedModel(data.current_model);\r\n        }\r\n      } catch (error) {\r\n        console.error('Error fetching current model:', error);\r\n      }\r\n    };\r\n\r\n    fetchCurrentModel();\r\n  }, []);\r\n\r\n  // avoid auto-scrolling on initial mount; only scroll when messages update after mount\r\n  const didMountRef = useRef(false);\r\n  useEffect(() => {\r\n    if (!didMountRef.current) {\r\n      didMountRef.current = true;\r\n      return;\r\n    }\r\n    // 使用页面滚动到底部，而不是元素内部滚动\r\n    setTimeout(() => {\r\n      window.scrollTo({\r\n        top: document.documentElement.scrollHeight,\r\n        behavior: 'smooth'\r\n      });\r\n    }, 100); // 小延迟确保DOM更新完成\r\n  }, [messages]);\r\n\r\n  const generateId = () => `msg-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;\r\n\r\n  const handleTypingComplete = (messageId: string) => {\r\n    setMessages(msgs =>\r\n      msgs.map(msg =>\r\n        msg.id === messageId ? { ...msg, isTyping: false } : msg\r\n      )\r\n    );\r\n  };\r\n\r\n  const handleSend = async () => {\r\n    if (!input.trim()) return;\r\n\r\n    const userMessage: Message = {\r\n      role: 'user',\r\n      content: input,\r\n      id: generateId(),\r\n      isTyping: false\r\n    };\r\n    const newMessages: Message[] = [...messages, userMessage];\r\n    setMessages(newMessages);\r\n    setInput('');\r\n\r\n    try {\r\n      const resp = await fetch('/api/chat', {\r\n        method: 'POST',\r\n        headers: { 'Content-Type': 'application/json' },\r\n        body: JSON.stringify({\r\n          question: input,\r\n          model: selectedModel, \r\n          messages: newMessages.map(m => ({\r\n            type: m.role === 'user' ? 'human' : 'ai',\r\n            content: m.content\r\n          }))\r\n        })\r\n      });\r\n      const data = await resp.json();\r\n      if (data && data.answer) {\r\n        const assistantMessage: Message = {\r\n          role: 'assistant',\r\n          content: data.answer,\r\n          id: generateId(),\r\n          isTyping: true,\r\n          documents: data.documents || []\r\n        };\r\n        setMessages(msgs => [...msgs, assistantMessage]);\r\n      }\r\n    } catch (e) {\r\n      const errorMessage: Message = {\r\n        role: 'assistant',\r\n        content: 'AI服务异常，请稍后重试。',\r\n        id: generateId(),\r\n        isTyping: true\r\n      };\r\n      setMessages(msgs => [...msgs, errorMessage]);\r\n    }\r\n  };\r\n\r\n  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {\r\n    if (e.key === 'Enter' && !e.shiftKey) {\r\n      e.preventDefault();\r\n      handleSend();\r\n    }\r\n  };\r\n\r\n  const handleTabChange = (tab: string) => {\r\n    setActiveTab(tab);\r\n    // 这里可以添加不同标签页的逻辑\r\n    console.log('切换到标签页:', tab);\r\n  };\r\n\r\n  const handleModelChange = async (model: string) => {\r\n    setSelectedModel(model);\r\n    console.log('Switching model to:', model);\r\n\r\n    try {\r\n      const response = await fetch('/api/switch-model', {\r\n        method: 'POST',\r\n        headers: { 'Content-Type': 'application/json' },\r\n        body: JSON.stringify({\r\n          model: model\r\n        })\r\n      });\r\n\r\n      const data = await response.json();\r\n      if (data.success) {\r\n        console.log(`Model switch successful: ${data.message}`);\r\n      } else {\r\n        console.error(`Model switch failed: ${data.message}`);\r\n      }\r\n    } catch (error) {\r\n      console.error('Model switch request failed:', error);\r\n    }\r\n  };\r\n\r\n  // 根据当前活动标签页渲染不同内容\r\n  const renderContent = () => {\r\n    switch (activeTab) {\r\n      case 'chat':\r\n        return (\r\n          <div className={styles.chatContainer}>\r\n            <div className={styles.chatMain}>\r\n              <div className={styles.messages}>\r\n                {messages.map((msg) => (\r\n                  <div\r\n                    key={msg.id}\r\n                    className={\r\n                      msg.role === 'user' ? styles.userMessage : styles.assistantMessage\r\n                    }\r\n                  >\r\n                    <div className={styles.messageContent}>\r\n                      {msg.role === 'assistant' && msg.isTyping ? (\r\n                        <TypeWriter\r\n                          text={msg.content}\r\n                          speed={30}\r\n                          allowSkip={true}\r\n                          onComplete={() => handleTypingComplete(msg.id)}\r\n                        />\r\n                      ) : (\r\n                        msg.content\r\n                      )}\r\n                    </div>\r\n                    {msg.role === 'assistant' && msg.documents && !msg.isTyping && (\r\n                      <DocumentsSection documents={msg.documents} />\r\n                    )}\r\n                  </div>\r\n                ))}\r\n                <div ref={messagesEndRef} />\r\n              </div>\r\n            </div>\r\n            <div className={styles.inputArea}>\r\n                <textarea\r\n                  className={styles.inputBox}\r\n                  value={input}\r\n                  onChange={e => setInput(e.target.value)}\r\n                  onKeyDown={handleKeyDown}\r\n                  placeholder=\"输入您的问题...\"\r\n                  rows={1}\r\n                />\r\n                <button className={styles.sendButton} onClick={handleSend} aria-label=\"发送\">\r\n                  <svg width=\"20\" height=\"20\" viewBox=\"0 0 20 20\" fill=\"currentColor\" xmlns=\"http://www.w3.org/2000/svg\"><path d=\"M8.99992 16V6.41407L5.70696 9.70704C5.31643 10.0976 4.68342 10.0976 4.29289 9.70704C3.90237 9.31652 3.90237 8.6835 4.29289 8.29298L9.29289 3.29298L9.36907 3.22462C9.76184 2.90427 10.3408 2.92686 10.707 3.29298L15.707 8.29298L15.7753 8.36915C16.0957 8.76192 16.0731 9.34092 15.707 9.70704C15.3408 10.0732 14.7618 10.0958 14.3691 9.7754L14.2929 9.70704L10.9999 6.41407V16C10.9999 16.5523 10.5522 17 9.99992 17C9.44764 17 8.99992 16.5523 8.99992 16Z\"></path></svg>\r\n                </button>\r\n            </div>\r\n          </div>\r\n        );\r\n      case 'code-review':\r\n        return (\r\n          <div className={styles.placeholderContent}>\r\n            <h2>Code Review Assistant</h2>\r\n            <p>代码审查助手功能正在开发中...</p>\r\n          </div>\r\n        );\r\n      case 'db-assistant':\r\n        return (\r\n          <div className={styles.placeholderContent}>\r\n            <h2>DB Assistant</h2>\r\n            <p>数据库助手功能正在开发中...</p>\r\n          </div>\r\n        );\r\n      case 'settings':\r\n        return (\r\n          <div className={styles.placeholderContent}>\r\n            <h2>设置</h2>\r\n            <p>系统设置功能正在开发中...</p>\r\n          </div>\r\n        );\r\n      default:\r\n        return null;\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className={styles.appContainer}>\r\n      <Sidebar activeTab={activeTab} onTabChange={handleTabChange} />\r\n\r\n      <div className={styles.modelSelectorContainer}>\r\n        <ModelSelector\r\n          selectedModel={selectedModel}\r\n          onModelChange={handleModelChange}\r\n        />\r\n      </div>\r\n\r\n      <div className={styles.mainContent}>\r\n        {renderContent()}\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;;AACA;AACA;AACA;AACA;AACA;AACA;;;AANA;;;;;;;AAsBA,MAAM,kBAA6B;IACjC;QACE,MAAM;QACN,SAAS;QACT,IAAI;QACJ,UAAU;IACZ;CACD;AAEc,SAAS;;IACtB,MAAM,CAAC,UAAU,YAAY,GAAG,IAAA,yKAAQ,EAAY;IACpD,MAAM,CAAC,OAAO,SAAS,GAAG,IAAA,yKAAQ,EAAC;IACnC,MAAM,CAAC,WAAW,aAAa,GAAG,IAAA,yKAAQ,EAAC;IAC3C,MAAM,CAAC,eAAe,iBAAiB,GAAG,IAAA,yKAAQ,EAAC;IACnD,MAAM,iBAAiB,IAAA,uKAAM,EAAiB;IAE9C,IAAA,0KAAS;8BAAC;YACR,MAAM;wDAAoB;oBACxB,IAAI;wBACF,MAAM,WAAW,MAAM,MAAM;wBAC7B,MAAM,OAAO,MAAM,SAAS,IAAI;wBAChC,IAAI,KAAK,aAAa,EAAE;4BACtB,iBAAiB,KAAK,aAAa;wBACrC;oBACF,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,iCAAiC;oBACjD;gBACF;;YAEA;QACF;6BAAG,EAAE;IAEL,sFAAsF;IACtF,MAAM,cAAc,IAAA,uKAAM,EAAC;IAC3B,IAAA,0KAAS;8BAAC;YACR,IAAI,CAAC,YAAY,OAAO,EAAE;gBACxB,YAAY,OAAO,GAAG;gBACtB;YACF;YACA,sBAAsB;YACtB;sCAAW;oBACT,OAAO,QAAQ,CAAC;wBACd,KAAK,SAAS,eAAe,CAAC,YAAY;wBAC1C,UAAU;oBACZ;gBACF;qCAAG,MAAM,eAAe;QAC1B;6BAAG;QAAC;KAAS;IAEb,MAAM,aAAa,IAAM,AAAC,OAAoB,OAAd,KAAK,GAAG,IAAG,KAA+C,OAA5C,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,SAAS,CAAC,GAAG;IAEtF,MAAM,uBAAuB,CAAC;QAC5B,YAAY,CAAA,OACV,KAAK,GAAG,CAAC,CAAA,MACP,IAAI,EAAE,KAAK,YAAY;oBAAE,GAAG,GAAG;oBAAE,UAAU;gBAAM,IAAI;IAG3D;IAEA,MAAM,aAAa;QACjB,IAAI,CAAC,MAAM,IAAI,IAAI;QAEnB,MAAM,cAAuB;YAC3B,MAAM;YACN,SAAS;YACT,IAAI;YACJ,UAAU;QACZ;QACA,MAAM,cAAyB;eAAI;YAAU;SAAY;QACzD,YAAY;QACZ,SAAS;QAET,IAAI;YACF,MAAM,OAAO,MAAM,MAAM,aAAa;gBACpC,QAAQ;gBACR,SAAS;oBAAE,gBAAgB;gBAAmB;gBAC9C,MAAM,KAAK,SAAS,CAAC;oBACnB,UAAU;oBACV,OAAO;oBACP,UAAU,YAAY,GAAG,CAAC,CAAA,IAAK,CAAC;4BAC9B,MAAM,EAAE,IAAI,KAAK,SAAS,UAAU;4BACpC,SAAS,EAAE,OAAO;wBACpB,CAAC;gBACH;YACF;YACA,MAAM,OAAO,MAAM,KAAK,IAAI;YAC5B,IAAI,QAAQ,KAAK,MAAM,EAAE;gBACvB,MAAM,mBAA4B;oBAChC,MAAM;oBACN,SAAS,KAAK,MAAM;oBACpB,IAAI;oBACJ,UAAU;oBACV,WAAW,KAAK,SAAS,IAAI,EAAE;gBACjC;gBACA,YAAY,CAAA,OAAQ;2BAAI;wBAAM;qBAAiB;YACjD;QACF,EAAE,OAAO,GAAG;YACV,MAAM,eAAwB;gBAC5B,MAAM;gBACN,SAAS;gBACT,IAAI;gBACJ,UAAU;YACZ;YACA,YAAY,CAAA,OAAQ;uBAAI;oBAAM;iBAAa;QAC7C;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,IAAI,EAAE,GAAG,KAAK,WAAW,CAAC,EAAE,QAAQ,EAAE;YACpC,EAAE,cAAc;YAChB;QACF;IACF;IAEA,MAAM,kBAAkB,CAAC;QACvB,aAAa;QACb,iBAAiB;QACjB,QAAQ,GAAG,CAAC,WAAW;IACzB;IAEA,MAAM,oBAAoB,OAAO;QAC/B,iBAAiB;QACjB,QAAQ,GAAG,CAAC,uBAAuB;QAEnC,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,qBAAqB;gBAChD,QAAQ;gBACR,SAAS;oBAAE,gBAAgB;gBAAmB;gBAC9C,MAAM,KAAK,SAAS,CAAC;oBACnB,OAAO;gBACT;YACF;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,IAAI,KAAK,OAAO,EAAE;gBAChB,QAAQ,GAAG,CAAC,AAAC,4BAAwC,OAAb,KAAK,OAAO;YACtD,OAAO;gBACL,QAAQ,KAAK,CAAC,AAAC,wBAAoC,OAAb,KAAK,OAAO;YACpD;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;QAChD;IACF;IAEA,kBAAkB;IAClB,MAAM,gBAAgB;QACpB,OAAQ;YACN,KAAK;gBACH,qBACE,6LAAC;oBAAI,WAAW,4IAAM,CAAC,aAAa;;sCAClC,6LAAC;4BAAI,WAAW,4IAAM,CAAC,QAAQ;sCAC7B,cAAA,6LAAC;gCAAI,WAAW,4IAAM,CAAC,QAAQ;;oCAC5B,SAAS,GAAG,CAAC,CAAC,oBACb,6LAAC;4CAEC,WACE,IAAI,IAAI,KAAK,SAAS,4IAAM,CAAC,WAAW,GAAG,4IAAM,CAAC,gBAAgB;;8DAGpE,6LAAC;oDAAI,WAAW,4IAAM,CAAC,cAAc;8DAClC,IAAI,IAAI,KAAK,eAAe,IAAI,QAAQ,iBACvC,6LAAC,qJAAU;wDACT,MAAM,IAAI,OAAO;wDACjB,OAAO;wDACP,WAAW;wDACX,YAAY,IAAM,qBAAqB,IAAI,EAAE;;;;;+DAG/C,IAAI,OAAO;;;;;;gDAGd,IAAI,IAAI,KAAK,eAAe,IAAI,SAAS,IAAI,CAAC,IAAI,QAAQ,kBACzD,6LAAC,2JAAgB;oDAAC,WAAW,IAAI,SAAS;;;;;;;2CAlBvC,IAAI,EAAE;;;;;kDAsBf,6LAAC;wCAAI,KAAK;;;;;;;;;;;;;;;;;sCAGd,6LAAC;4BAAI,WAAW,4IAAM,CAAC,SAAS;;8CAC5B,6LAAC;oCACC,WAAW,4IAAM,CAAC,QAAQ;oCAC1B,OAAO;oCACP,UAAU,CAAA,IAAK,SAAS,EAAE,MAAM,CAAC,KAAK;oCACtC,WAAW;oCACX,aAAY;oCACZ,MAAM;;;;;;8CAER,6LAAC;oCAAO,WAAW,4IAAM,CAAC,UAAU;oCAAE,SAAS;oCAAY,cAAW;8CACpE,cAAA,6LAAC;wCAAI,OAAM;wCAAK,QAAO;wCAAK,SAAQ;wCAAY,MAAK;wCAAe,OAAM;kDAA6B,cAAA,6LAAC;4CAAK,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAK3H,KAAK;gBACH,qBACE,6LAAC;oBAAI,WAAW,4IAAM,CAAC,kBAAkB;;sCACvC,6LAAC;sCAAG;;;;;;sCACJ,6LAAC;sCAAE;;;;;;;;;;;;YAGT,KAAK;gBACH,qBACE,6LAAC;oBAAI,WAAW,4IAAM,CAAC,kBAAkB;;sCACvC,6LAAC;sCAAG;;;;;;sCACJ,6LAAC;sCAAE;;;;;;;;;;;;YAGT,KAAK;gBACH,qBACE,6LAAC;oBAAI,WAAW,4IAAM,CAAC,kBAAkB;;sCACvC,6LAAC;sCAAG;;;;;;sCACJ,6LAAC;sCAAE;;;;;;;;;;;;YAGT;gBACE,OAAO;QACX;IACF;IAEA,qBACE,6LAAC;QAAI,WAAW,4IAAM,CAAC,YAAY;;0BACjC,6LAAC,kJAAO;gBAAC,WAAW;gBAAW,aAAa;;;;;;0BAE5C,6LAAC;gBAAI,WAAW,4IAAM,CAAC,sBAAsB;0BAC3C,cAAA,6LAAC,wJAAa;oBACZ,eAAe;oBACf,eAAe;;;;;;;;;;;0BAInB,6LAAC;gBAAI,WAAW,4IAAM,CAAC,WAAW;0BAC/B;;;;;;;;;;;;AAIT;GAlOwB;KAAA", "debugId": null}}, {"offset": {"line": 1156, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/AI/MetaRAG/metarag/frontend/src/app/page.tsx"], "sourcesContent": ["\"use client\";\nimport ChatPage from './ChatPage';\n\nexport default function Home() {\n  return <ChatPage />;\n}\n"], "names": [], "mappings": ";;;;;AACA;AADA;;;AAGe,SAAS;IACtB,qBAAO,6LAAC,qIAAQ;;;;;AAClB;KAFwB", "debugId": null}}, {"offset": {"line": 1182, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/AI/MetaRAG/metarag/frontend/node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js"], "sourcesContent": ["/**\n * @license React\n * react-jsx-dev-runtime.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\"use strict\";\n\"production\" !== process.env.NODE_ENV &&\n  (function () {\n    function getComponentNameFromType(type) {\n      if (null == type) return null;\n      if (\"function\" === typeof type)\n        return type.$$typeof === REACT_CLIENT_REFERENCE\n          ? null\n          : type.displayName || type.name || null;\n      if (\"string\" === typeof type) return type;\n      switch (type) {\n        case REACT_FRAGMENT_TYPE:\n          return \"Fragment\";\n        case REACT_PROFILER_TYPE:\n          return \"Profiler\";\n        case REACT_STRICT_MODE_TYPE:\n          return \"StrictMode\";\n        case REACT_SUSPENSE_TYPE:\n          return \"Suspense\";\n        case REACT_SUSPENSE_LIST_TYPE:\n          return \"SuspenseList\";\n        case REACT_ACTIVITY_TYPE:\n          return \"Activity\";\n      }\n      if (\"object\" === typeof type)\n        switch (\n          (\"number\" === typeof type.tag &&\n            console.error(\n              \"Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue.\"\n            ),\n          type.$$typeof)\n        ) {\n          case REACT_PORTAL_TYPE:\n            return \"Portal\";\n          case REACT_CONTEXT_TYPE:\n            return type.displayName || \"Context\";\n          case REACT_CONSUMER_TYPE:\n            return (type._context.displayName || \"Context\") + \".Consumer\";\n          case REACT_FORWARD_REF_TYPE:\n            var innerType = type.render;\n            type = type.displayName;\n            type ||\n              ((type = innerType.displayName || innerType.name || \"\"),\n              (type = \"\" !== type ? \"ForwardRef(\" + type + \")\" : \"ForwardRef\"));\n            return type;\n          case REACT_MEMO_TYPE:\n            return (\n              (innerType = type.displayName || null),\n              null !== innerType\n                ? innerType\n                : getComponentNameFromType(type.type) || \"Memo\"\n            );\n          case REACT_LAZY_TYPE:\n            innerType = type._payload;\n            type = type._init;\n            try {\n              return getComponentNameFromType(type(innerType));\n            } catch (x) {}\n        }\n      return null;\n    }\n    function testStringCoercion(value) {\n      return \"\" + value;\n    }\n    function checkKeyStringCoercion(value) {\n      try {\n        testStringCoercion(value);\n        var JSCompiler_inline_result = !1;\n      } catch (e) {\n        JSCompiler_inline_result = !0;\n      }\n      if (JSCompiler_inline_result) {\n        JSCompiler_inline_result = console;\n        var JSCompiler_temp_const = JSCompiler_inline_result.error;\n        var JSCompiler_inline_result$jscomp$0 =\n          (\"function\" === typeof Symbol &&\n            Symbol.toStringTag &&\n            value[Symbol.toStringTag]) ||\n          value.constructor.name ||\n          \"Object\";\n        JSCompiler_temp_const.call(\n          JSCompiler_inline_result,\n          \"The provided key is an unsupported type %s. This value must be coerced to a string before using it here.\",\n          JSCompiler_inline_result$jscomp$0\n        );\n        return testStringCoercion(value);\n      }\n    }\n    function getTaskName(type) {\n      if (type === REACT_FRAGMENT_TYPE) return \"<>\";\n      if (\n        \"object\" === typeof type &&\n        null !== type &&\n        type.$$typeof === REACT_LAZY_TYPE\n      )\n        return \"<...>\";\n      try {\n        var name = getComponentNameFromType(type);\n        return name ? \"<\" + name + \">\" : \"<...>\";\n      } catch (x) {\n        return \"<...>\";\n      }\n    }\n    function getOwner() {\n      var dispatcher = ReactSharedInternals.A;\n      return null === dispatcher ? null : dispatcher.getOwner();\n    }\n    function UnknownOwner() {\n      return Error(\"react-stack-top-frame\");\n    }\n    function hasValidKey(config) {\n      if (hasOwnProperty.call(config, \"key\")) {\n        var getter = Object.getOwnPropertyDescriptor(config, \"key\").get;\n        if (getter && getter.isReactWarning) return !1;\n      }\n      return void 0 !== config.key;\n    }\n    function defineKeyPropWarningGetter(props, displayName) {\n      function warnAboutAccessingKey() {\n        specialPropKeyWarningShown ||\n          ((specialPropKeyWarningShown = !0),\n          console.error(\n            \"%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://react.dev/link/special-props)\",\n            displayName\n          ));\n      }\n      warnAboutAccessingKey.isReactWarning = !0;\n      Object.defineProperty(props, \"key\", {\n        get: warnAboutAccessingKey,\n        configurable: !0\n      });\n    }\n    function elementRefGetterWithDeprecationWarning() {\n      var componentName = getComponentNameFromType(this.type);\n      didWarnAboutElementRef[componentName] ||\n        ((didWarnAboutElementRef[componentName] = !0),\n        console.error(\n          \"Accessing element.ref was removed in React 19. ref is now a regular prop. It will be removed from the JSX Element type in a future release.\"\n        ));\n      componentName = this.props.ref;\n      return void 0 !== componentName ? componentName : null;\n    }\n    function ReactElement(type, key, props, owner, debugStack, debugTask) {\n      var refProp = props.ref;\n      type = {\n        $$typeof: REACT_ELEMENT_TYPE,\n        type: type,\n        key: key,\n        props: props,\n        _owner: owner\n      };\n      null !== (void 0 !== refProp ? refProp : null)\n        ? Object.defineProperty(type, \"ref\", {\n            enumerable: !1,\n            get: elementRefGetterWithDeprecationWarning\n          })\n        : Object.defineProperty(type, \"ref\", { enumerable: !1, value: null });\n      type._store = {};\n      Object.defineProperty(type._store, \"validated\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: 0\n      });\n      Object.defineProperty(type, \"_debugInfo\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: null\n      });\n      Object.defineProperty(type, \"_debugStack\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugStack\n      });\n      Object.defineProperty(type, \"_debugTask\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugTask\n      });\n      Object.freeze && (Object.freeze(type.props), Object.freeze(type));\n      return type;\n    }\n    function jsxDEVImpl(\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      debugStack,\n      debugTask\n    ) {\n      var children = config.children;\n      if (void 0 !== children)\n        if (isStaticChildren)\n          if (isArrayImpl(children)) {\n            for (\n              isStaticChildren = 0;\n              isStaticChildren < children.length;\n              isStaticChildren++\n            )\n              validateChildKeys(children[isStaticChildren]);\n            Object.freeze && Object.freeze(children);\n          } else\n            console.error(\n              \"React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.\"\n            );\n        else validateChildKeys(children);\n      if (hasOwnProperty.call(config, \"key\")) {\n        children = getComponentNameFromType(type);\n        var keys = Object.keys(config).filter(function (k) {\n          return \"key\" !== k;\n        });\n        isStaticChildren =\n          0 < keys.length\n            ? \"{key: someKey, \" + keys.join(\": ..., \") + \": ...}\"\n            : \"{key: someKey}\";\n        didWarnAboutKeySpread[children + isStaticChildren] ||\n          ((keys =\n            0 < keys.length ? \"{\" + keys.join(\": ..., \") + \": ...}\" : \"{}\"),\n          console.error(\n            'A props object containing a \"key\" prop is being spread into JSX:\\n  let props = %s;\\n  <%s {...props} />\\nReact keys must be passed directly to JSX without using spread:\\n  let props = %s;\\n  <%s key={someKey} {...props} />',\n            isStaticChildren,\n            children,\n            keys,\n            children\n          ),\n          (didWarnAboutKeySpread[children + isStaticChildren] = !0));\n      }\n      children = null;\n      void 0 !== maybeKey &&\n        (checkKeyStringCoercion(maybeKey), (children = \"\" + maybeKey));\n      hasValidKey(config) &&\n        (checkKeyStringCoercion(config.key), (children = \"\" + config.key));\n      if (\"key\" in config) {\n        maybeKey = {};\n        for (var propName in config)\n          \"key\" !== propName && (maybeKey[propName] = config[propName]);\n      } else maybeKey = config;\n      children &&\n        defineKeyPropWarningGetter(\n          maybeKey,\n          \"function\" === typeof type\n            ? type.displayName || type.name || \"Unknown\"\n            : type\n        );\n      return ReactElement(\n        type,\n        children,\n        maybeKey,\n        getOwner(),\n        debugStack,\n        debugTask\n      );\n    }\n    function validateChildKeys(node) {\n      \"object\" === typeof node &&\n        null !== node &&\n        node.$$typeof === REACT_ELEMENT_TYPE &&\n        node._store &&\n        (node._store.validated = 1);\n    }\n    var React = require(\"next/dist/compiled/react\"),\n      REACT_ELEMENT_TYPE = Symbol.for(\"react.transitional.element\"),\n      REACT_PORTAL_TYPE = Symbol.for(\"react.portal\"),\n      REACT_FRAGMENT_TYPE = Symbol.for(\"react.fragment\"),\n      REACT_STRICT_MODE_TYPE = Symbol.for(\"react.strict_mode\"),\n      REACT_PROFILER_TYPE = Symbol.for(\"react.profiler\"),\n      REACT_CONSUMER_TYPE = Symbol.for(\"react.consumer\"),\n      REACT_CONTEXT_TYPE = Symbol.for(\"react.context\"),\n      REACT_FORWARD_REF_TYPE = Symbol.for(\"react.forward_ref\"),\n      REACT_SUSPENSE_TYPE = Symbol.for(\"react.suspense\"),\n      REACT_SUSPENSE_LIST_TYPE = Symbol.for(\"react.suspense_list\"),\n      REACT_MEMO_TYPE = Symbol.for(\"react.memo\"),\n      REACT_LAZY_TYPE = Symbol.for(\"react.lazy\"),\n      REACT_ACTIVITY_TYPE = Symbol.for(\"react.activity\"),\n      REACT_CLIENT_REFERENCE = Symbol.for(\"react.client.reference\"),\n      ReactSharedInternals =\n        React.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,\n      hasOwnProperty = Object.prototype.hasOwnProperty,\n      isArrayImpl = Array.isArray,\n      createTask = console.createTask\n        ? console.createTask\n        : function () {\n            return null;\n          };\n    React = {\n      react_stack_bottom_frame: function (callStackForError) {\n        return callStackForError();\n      }\n    };\n    var specialPropKeyWarningShown;\n    var didWarnAboutElementRef = {};\n    var unknownOwnerDebugStack = React.react_stack_bottom_frame.bind(\n      React,\n      UnknownOwner\n    )();\n    var unknownOwnerDebugTask = createTask(getTaskName(UnknownOwner));\n    var didWarnAboutKeySpread = {};\n    exports.Fragment = REACT_FRAGMENT_TYPE;\n    exports.jsxDEV = function (type, config, maybeKey, isStaticChildren) {\n      var trackActualOwner =\n        1e4 > ReactSharedInternals.recentlyCreatedOwnerStacks++;\n      return jsxDEVImpl(\n        type,\n        config,\n        maybeKey,\n        isStaticChildren,\n        trackActualOwner\n          ? Error(\"react-stack-top-frame\")\n          : unknownOwnerDebugStack,\n        trackActualOwner ? createTask(getTaskName(type)) : unknownOwnerDebugTask\n      );\n    };\n  })();\n"], "names": [], "mappings": "AAAA;;;;;;;;CAQC,GAGgB;AADjB;AACA,oEACE,AAAC;IACC,SAAS,yBAAyB,IAAI;QACpC,IAAI,QAAQ,MAAM,OAAO;QACzB,IAAI,eAAe,OAAO,MACxB,OAAO,KAAK,QAAQ,KAAK,yBACrB,OACA,KAAK,WAAW,IAAI,KAAK,IAAI,IAAI;QACvC,IAAI,aAAa,OAAO,MAAM,OAAO;QACrC,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;QACX;QACA,IAAI,aAAa,OAAO,MACtB,OACG,aAAa,OAAO,KAAK,GAAG,IAC3B,QAAQ,KAAK,CACX,sHAEJ,KAAK,QAAQ;YAEb,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO,KAAK,WAAW,IAAI;YAC7B,KAAK;gBACH,OAAO,CAAC,KAAK,QAAQ,CAAC,WAAW,IAAI,SAAS,IAAI;YACpD,KAAK;gBACH,IAAI,YAAY,KAAK,MAAM;gBAC3B,OAAO,KAAK,WAAW;gBACvB,QACE,CAAC,AAAC,OAAO,UAAU,WAAW,IAAI,UAAU,IAAI,IAAI,IACnD,OAAO,OAAO,OAAO,gBAAgB,OAAO,MAAM,YAAa;gBAClE,OAAO;YACT,KAAK;gBACH,OACE,AAAC,YAAY,KAAK,WAAW,IAAI,MACjC,SAAS,YACL,YACA,yBAAyB,KAAK,IAAI,KAAK;YAE/C,KAAK;gBACH,YAAY,KAAK,QAAQ;gBACzB,OAAO,KAAK,KAAK;gBACjB,IAAI;oBACF,OAAO,yBAAyB,KAAK;gBACvC,EAAE,OAAO,GAAG,CAAC;QACjB;QACF,OAAO;IACT;IACA,SAAS,mBAAmB,KAAK;QAC/B,OAAO,KAAK;IACd;IACA,SAAS,uBAAuB,KAAK;QACnC,IAAI;YACF,mBAAmB;YACnB,IAAI,2BAA2B,CAAC;QAClC,EAAE,OAAO,GAAG;YACV,2BAA2B,CAAC;QAC9B;QACA,IAAI,0BAA0B;YAC5B,2BAA2B;YAC3B,IAAI,wBAAwB,yBAAyB,KAAK;YAC1D,IAAI,oCACF,AAAC,eAAe,OAAO,UACrB,OAAO,WAAW,IAClB,KAAK,CAAC,OAAO,WAAW,CAAC,IAC3B,MAAM,WAAW,CAAC,IAAI,IACtB;YACF,sBAAsB,IAAI,CACxB,0BACA,4GACA;YAEF,OAAO,mBAAmB;QAC5B;IACF;IACA,SAAS,YAAY,IAAI;QACvB,IAAI,SAAS,qBAAqB,OAAO;QACzC,IACE,aAAa,OAAO,QACpB,SAAS,QACT,KAAK,QAAQ,KAAK,iBAElB,OAAO;QACT,IAAI;YACF,IAAI,OAAO,yBAAyB;YACpC,OAAO,OAAO,MAAM,OAAO,MAAM;QACnC,EAAE,OAAO,GAAG;YACV,OAAO;QACT;IACF;IACA,SAAS;QACP,IAAI,aAAa,qBAAqB,CAAC;QACvC,OAAO,SAAS,aAAa,OAAO,WAAW,QAAQ;IACzD;IACA,SAAS;QACP,OAAO,MAAM;IACf;IACA,SAAS,YAAY,MAAM;QACzB,IAAI,eAAe,IAAI,CAAC,QAAQ,QAAQ;YACtC,IAAI,SAAS,OAAO,wBAAwB,CAAC,QAAQ,OAAO,GAAG;YAC/D,IAAI,UAAU,OAAO,cAAc,EAAE,OAAO,CAAC;QAC/C;QACA,OAAO,KAAK,MAAM,OAAO,GAAG;IAC9B;IACA,SAAS,2BAA2B,KAAK,EAAE,WAAW;QACpD,SAAS;YACP,8BACE,CAAC,AAAC,6BAA6B,CAAC,GAChC,QAAQ,KAAK,CACX,2OACA,YACD;QACL;QACA,sBAAsB,cAAc,GAAG,CAAC;QACxC,OAAO,cAAc,CAAC,OAAO,OAAO;YAClC,KAAK;YACL,cAAc,CAAC;QACjB;IACF;IACA,SAAS;QACP,IAAI,gBAAgB,yBAAyB,IAAI,CAAC,IAAI;QACtD,sBAAsB,CAAC,cAAc,IACnC,CAAC,AAAC,sBAAsB,CAAC,cAAc,GAAG,CAAC,GAC3C,QAAQ,KAAK,CACX,8IACD;QACH,gBAAgB,IAAI,CAAC,KAAK,CAAC,GAAG;QAC9B,OAAO,KAAK,MAAM,gBAAgB,gBAAgB;IACpD;IACA,SAAS,aAAa,IAAI,EAAE,GAAG,EAAE,KAAK,EAAE,KAAK,EAAE,UAAU,EAAE,SAAS;QAClE,IAAI,UAAU,MAAM,GAAG;QACvB,OAAO;YACL,UAAU;YACV,MAAM;YACN,KAAK;YACL,OAAO;YACP,QAAQ;QACV;QACA,SAAS,CAAC,KAAK,MAAM,UAAU,UAAU,IAAI,IACzC,OAAO,cAAc,CAAC,MAAM,OAAO;YACjC,YAAY,CAAC;YACb,KAAK;QACP,KACA,OAAO,cAAc,CAAC,MAAM,OAAO;YAAE,YAAY,CAAC;YAAG,OAAO;QAAK;QACrE,KAAK,MAAM,GAAG,CAAC;QACf,OAAO,cAAc,CAAC,KAAK,MAAM,EAAE,aAAa;YAC9C,cAAc,CAAC;YACf,YAAY,CAAC;YACb,UAAU,CAAC;YACX,OAAO;QACT;QACA,OAAO,cAAc,CAAC,MAAM,cAAc;YACxC,cAAc,CAAC;YACf,YAAY,CAAC;YACb,UAAU,CAAC;YACX,OAAO;QACT;QACA,OAAO,cAAc,CAAC,MAAM,eAAe;YACzC,cAAc,CAAC;YACf,YAAY,CAAC;YACb,UAAU,CAAC;YACX,OAAO;QACT;QACA,OAAO,cAAc,CAAC,MAAM,cAAc;YACxC,cAAc,CAAC;YACf,YAAY,CAAC;YACb,UAAU,CAAC;YACX,OAAO;QACT;QACA,OAAO,MAAM,IAAI,CAAC,OAAO,MAAM,CAAC,KAAK,KAAK,GAAG,OAAO,MAAM,CAAC,KAAK;QAChE,OAAO;IACT;IACA,SAAS,WACP,IAAI,EACJ,MAAM,EACN,QAAQ,EACR,gBAAgB,EAChB,UAAU,EACV,SAAS;QAET,IAAI,WAAW,OAAO,QAAQ;QAC9B,IAAI,KAAK,MAAM,UACb,IAAI,kBACF,IAAI,YAAY,WAAW;YACzB,IACE,mBAAmB,GACnB,mBAAmB,SAAS,MAAM,EAClC,mBAEA,kBAAkB,QAAQ,CAAC,iBAAiB;YAC9C,OAAO,MAAM,IAAI,OAAO,MAAM,CAAC;QACjC,OACE,QAAQ,KAAK,CACX;aAED,kBAAkB;QACzB,IAAI,eAAe,IAAI,CAAC,QAAQ,QAAQ;YACtC,WAAW,yBAAyB;YACpC,IAAI,OAAO,OAAO,IAAI,CAAC,QAAQ,MAAM,CAAC,SAAU,CAAC;gBAC/C,OAAO,UAAU;YACnB;YACA,mBACE,IAAI,KAAK,MAAM,GACX,oBAAoB,KAAK,IAAI,CAAC,aAAa,WAC3C;YACN,qBAAqB,CAAC,WAAW,iBAAiB,IAChD,CAAC,AAAC,OACA,IAAI,KAAK,MAAM,GAAG,MAAM,KAAK,IAAI,CAAC,aAAa,WAAW,MAC5D,QAAQ,KAAK,CACX,mOACA,kBACA,UACA,MACA,WAED,qBAAqB,CAAC,WAAW,iBAAiB,GAAG,CAAC,CAAE;QAC7D;QACA,WAAW;QACX,KAAK,MAAM,YACT,CAAC,uBAAuB,WAAY,WAAW,KAAK,QAAS;QAC/D,YAAY,WACV,CAAC,uBAAuB,OAAO,GAAG,GAAI,WAAW,KAAK,OAAO,GAAG,AAAC;QACnE,IAAI,SAAS,QAAQ;YACnB,WAAW,CAAC;YACZ,IAAK,IAAI,YAAY,OACnB,UAAU,YAAY,CAAC,QAAQ,CAAC,SAAS,GAAG,MAAM,CAAC,SAAS;QAChE,OAAO,WAAW;QAClB,YACE,2BACE,UACA,eAAe,OAAO,OAClB,KAAK,WAAW,IAAI,KAAK,IAAI,IAAI,YACjC;QAER,OAAO,aACL,MACA,UACA,UACA,YACA,YACA;IAEJ;IACA,SAAS,kBAAkB,IAAI;QAC7B,aAAa,OAAO,QAClB,SAAS,QACT,KAAK,QAAQ,KAAK,sBAClB,KAAK,MAAM,IACX,CAAC,KAAK,MAAM,CAAC,SAAS,GAAG,CAAC;IAC9B;IACA,IAAI,uHACF,qBAAqB,OAAO,GAAG,CAAC,+BAChC,oBAAoB,OAAO,GAAG,CAAC,iBAC/B,sBAAsB,OAAO,GAAG,CAAC,mBACjC,yBAAyB,OAAO,GAAG,CAAC,sBACpC,sBAAsB,OAAO,GAAG,CAAC,mBACjC,sBAAsB,OAAO,GAAG,CAAC,mBACjC,qBAAqB,OAAO,GAAG,CAAC,kBAChC,yBAAyB,OAAO,GAAG,CAAC,sBACpC,sBAAsB,OAAO,GAAG,CAAC,mBACjC,2BAA2B,OAAO,GAAG,CAAC,wBACtC,kBAAkB,OAAO,GAAG,CAAC,eAC7B,kBAAkB,OAAO,GAAG,CAAC,eAC7B,sBAAsB,OAAO,GAAG,CAAC,mBACjC,yBAAyB,OAAO,GAAG,CAAC,2BACpC,uBACE,MAAM,+DAA+D,EACvE,iBAAiB,OAAO,SAAS,CAAC,cAAc,EAChD,cAAc,MAAM,OAAO,EAC3B,aAAa,QAAQ,UAAU,GAC3B,QAAQ,UAAU,GAClB;QACE,OAAO;IACT;IACN,QAAQ;QACN,0BAA0B,SAAU,iBAAiB;YACnD,OAAO;QACT;IACF;IACA,IAAI;IACJ,IAAI,yBAAyB,CAAC;IAC9B,IAAI,yBAAyB,MAAM,wBAAwB,CAAC,IAAI,CAC9D,OACA;IAEF,IAAI,wBAAwB,WAAW,YAAY;IACnD,IAAI,wBAAwB,CAAC;IAC7B,QAAQ,QAAQ,GAAG;IACnB,QAAQ,MAAM,GAAG,SAAU,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,gBAAgB;QACjE,IAAI,mBACF,MAAM,qBAAqB,0BAA0B;QACvD,OAAO,WACL,MACA,QACA,UACA,kBACA,mBACI,MAAM,2BACN,wBACJ,mBAAmB,WAAW,YAAY,SAAS;IAEvD;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1387, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/AI/MetaRAG/metarag/frontend/node_modules/next/dist/compiled/react/jsx-dev-runtime.js"], "sourcesContent": ["'use strict';\n\nif (process.env.NODE_ENV === 'production') {\n  module.exports = require('./cjs/react-jsx-dev-runtime.production.js');\n} else {\n  module.exports = require('./cjs/react-jsx-dev-runtime.development.js');\n}\n"], "names": [], "mappings": "AAEI;AAFJ;AAEA;;KAEO;IACL,OAAO,OAAO;AAChB", "ignoreList": [0], "debugId": null}}]}