"use client";
import React, { useState } from 'react';
import styles from './DocumentsSection.module.css';

interface Document {
  title: string;
  page_content: string;
  metadata: Record<string, any>;
}

interface DocumentsSectionProps {
  documents: Document[];
}

const DocumentsSection: React.FC<DocumentsSectionProps> = ({ documents }) => {
  const [isExpanded, setIsExpanded] = useState(false);

  if (!documents || documents.length === 0) {
    return (
      <div className={styles.documentsSection}>
        <div className={styles.noDocuments}>
          This answer is based on the AI's own knowledge. No relevant documents in the knowledge base.
        </div>
      </div>
    );
  }

  return (
    <div className={styles.documentsSection}>
      <div 
        className={styles.documentsHeader}
        onClick={() => setIsExpanded(!isExpanded)}
      >
        <span className={styles.documentsTitle}>
          📚 Related Documents ({documents.length})
        </span>
        <span className={`${styles.expandIcon} ${isExpanded ? styles.expanded : ''}`}>
          ▼
        </span>
      </div>
      
      {isExpanded && (
        <div className={styles.documentsContent}>
          {documents.map((doc, index) => (
            <div key={index} className={styles.documentItem}>
              <div className={styles.documentTitle}>
                {doc.title || `Document ${index + 1}`}
              </div>
              <div className={styles.documentContent}>
                {doc.page_content}
              </div>
              {doc.metadata && Object.keys(doc.metadata).length > 0 && (
                <div className={styles.documentMetadata}>
                  <strong>Metadata:</strong>
                  <pre>{JSON.stringify(doc.metadata, null, 2)}</pre>
                </div>
              )}
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default DocumentsSection;
