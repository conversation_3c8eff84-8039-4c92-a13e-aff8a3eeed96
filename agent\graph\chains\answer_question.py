from langchain_core.output_parsers import StrOutputParser
from langchain.prompts import PromptTemplate

rag_prompt_template = """
You are an assistant for question-answering tasks. 
You MUST use the following pieces of retrieved context to answer the question. 
If you cannot find the answer based on the retrieved context, just answer the question with your own knowledge. 
If you don't know the answer, say that you don't know the answer. Please do not make up an answer.
If you can find the answer based on the retrieved context, use three sentences maximum and keep the answer concise.
Question: {question} 
Context: {context} 
Answer:
"""
rag_prompt = PromptTemplate(template=rag_prompt_template, input_variables=["question", "context"])

def get_answer_creator():
    from agent.utility.llm_factory import llm
    return rag_prompt | llm | StrOutputParser()
