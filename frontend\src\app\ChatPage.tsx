"use client";
import React, { useState, useRef, useEffect } from 'react';
import styles from './chat.module.css';
import TypeWriter from './components/TypeWriter';
import Sidebar from './components/Sidebar';
import ModelSelector from './components/ModelSelector';
import DocumentsSection from './components/DocumentsSection';

interface Document {
  title: string;
  page_content: string;
  metadata: Record<string, any>;
}

interface Message {
  role: 'user' | 'assistant';
  content: string;
  id: string;
  isTyping?: boolean;
  documents?: Document[];
}

const initialMessages: Message[] = [
  {
    role: 'assistant',
    content: '你好！我是AI助手，有什么可以帮您？',
    id: 'initial-1',
    isTyping: true
  },
];

export default function ChatPage() {
  const [messages, setMessages] = useState<Message[]>(initialMessages);
  const [input, setInput] = useState('');
  const [activeTab, setActiveTab] = useState('chat');
  const [selectedModel, setSelectedModel] = useState('qwen3:8b');
  const messagesEndRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const fetchCurrentModel = async () => {
      try {
        const response = await fetch('/api/current-model');
        const data = await response.json();
        if (data.current_model) {
          setSelectedModel(data.current_model);
        }
      } catch (error) {
        console.error('Error fetching current model:', error);
      }
    };

    fetchCurrentModel();
  }, []);

  // avoid auto-scrolling on initial mount; only scroll when messages update after mount
  const didMountRef = useRef(false);
  useEffect(() => {
    if (!didMountRef.current) {
      didMountRef.current = true;
      return;
    }
    // 使用页面滚动到底部，而不是元素内部滚动
    setTimeout(() => {
      window.scrollTo({
        top: document.documentElement.scrollHeight,
        behavior: 'smooth'
      });
    }, 100); // 小延迟确保DOM更新完成
  }, [messages]);

  const generateId = () => `msg-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;

  const handleTypingComplete = (messageId: string) => {
    setMessages(msgs =>
      msgs.map(msg =>
        msg.id === messageId ? { ...msg, isTyping: false } : msg
      )
    );
  };

  const handleSend = async () => {
    if (!input.trim()) return;

    const userMessage: Message = {
      role: 'user',
      content: input,
      id: generateId(),
      isTyping: false
    };
    const newMessages: Message[] = [...messages, userMessage];
    setMessages(newMessages);
    setInput('');

    try {
      const resp = await fetch('/api/chat', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          question: input,
          model: selectedModel, 
          messages: newMessages.map(m => ({
            type: m.role === 'user' ? 'human' : 'ai',
            content: m.content
          }))
        })
      });
      const data = await resp.json();
      if (data && data.answer) {
        const assistantMessage: Message = {
          role: 'assistant',
          content: data.answer,
          id: generateId(),
          isTyping: true,
          documents: data.documents || []
        };
        setMessages(msgs => [...msgs, assistantMessage]);
      }
    } catch (e) {
      const errorMessage: Message = {
        role: 'assistant',
        content: 'AI服务异常，请稍后重试。',
        id: generateId(),
        isTyping: true
      };
      setMessages(msgs => [...msgs, errorMessage]);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSend();
    }
  };

  const handleTabChange = (tab: string) => {
    setActiveTab(tab);
    // 这里可以添加不同标签页的逻辑
    console.log('切换到标签页:', tab);
  };

  const handleModelChange = async (model: string) => {
    setSelectedModel(model);
    console.log('Switching model to:', model);

    try {
      const response = await fetch('/api/switch-model', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          model: model
        })
      });

      const data = await response.json();
      if (data.success) {
        console.log(`Model switch successful: ${data.message}`);
      } else {
        console.error(`Model switch failed: ${data.message}`);
      }
    } catch (error) {
      console.error('Model switch request failed:', error);
    }
  };

  // 根据当前活动标签页渲染不同内容
  const renderContent = () => {
    switch (activeTab) {
      case 'chat':
        return (
          <div className={styles.chatContainer}>
            <div className={styles.chatMain}>
              <div className={styles.messages}>
                {messages.map((msg) => (
                  <div
                    key={msg.id}
                    className={
                      msg.role === 'user' ? styles.userMessage : styles.assistantMessage
                    }
                  >
                    <div className={styles.messageContent}>
                      {msg.role === 'assistant' && msg.isTyping ? (
                        <TypeWriter
                          text={msg.content}
                          speed={30}
                          allowSkip={true}
                          onComplete={() => handleTypingComplete(msg.id)}
                        />
                      ) : (
                        msg.content
                      )}
                    </div>
                    {msg.role === 'assistant' && msg.documents && !msg.isTyping && (
                      <DocumentsSection documents={msg.documents} />
                    )}
                  </div>
                ))}
                <div ref={messagesEndRef} />
              </div>
            </div>
            <div className={styles.inputArea}>
                <textarea
                  className={styles.inputBox}
                  value={input}
                  onChange={e => setInput(e.target.value)}
                  onKeyDown={handleKeyDown}
                  placeholder="输入您的问题..."
                  rows={1}
                />
                <button className={styles.sendButton} onClick={handleSend} aria-label="发送">
                  <svg width="20" height="20" viewBox="0 0 20 20" fill="currentColor" xmlns="http://www.w3.org/2000/svg"><path d="M8.99992 16V6.41407L5.70696 9.70704C5.31643 10.0976 4.68342 10.0976 4.29289 9.70704C3.90237 9.31652 3.90237 8.6835 4.29289 8.29298L9.29289 3.29298L9.36907 3.22462C9.76184 2.90427 10.3408 2.92686 10.707 3.29298L15.707 8.29298L15.7753 8.36915C16.0957 8.76192 16.0731 9.34092 15.707 9.70704C15.3408 10.0732 14.7618 10.0958 14.3691 9.7754L14.2929 9.70704L10.9999 6.41407V16C10.9999 16.5523 10.5522 17 9.99992 17C9.44764 17 8.99992 16.5523 8.99992 16Z"></path></svg>
                </button>
            </div>
          </div>
        );
      case 'code-review':
        return (
          <div className={styles.placeholderContent}>
            <h2>Code Review Assistant</h2>
            <p>代码审查助手功能正在开发中...</p>
          </div>
        );
      case 'db-assistant':
        return (
          <div className={styles.placeholderContent}>
            <h2>DB Assistant</h2>
            <p>数据库助手功能正在开发中...</p>
          </div>
        );
      case 'settings':
        return (
          <div className={styles.placeholderContent}>
            <h2>设置</h2>
            <p>系统设置功能正在开发中...</p>
          </div>
        );
      default:
        return null;
    }
  };

  return (
    <div className={styles.appContainer}>
      <Sidebar activeTab={activeTab} onTabChange={handleTabChange} />

      <div className={styles.modelSelectorContainer}>
        <ModelSelector
          selectedModel={selectedModel}
          onModelChange={handleModelChange}
        />
      </div>

      <div className={styles.mainContent}>
        {renderContent()}
      </div>
    </div>
  );
}
