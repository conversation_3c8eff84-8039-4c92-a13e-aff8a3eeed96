{"version": 3, "sources": [], "sections": [{"offset": {"line": 9, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/app/chat.module.css [app-ssr] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"appContainer\": \"chat-module__Ix5oLa__appContainer\",\n  \"assistantMessage\": \"chat-module__Ix5oLa__assistantMessage\",\n  \"chatContainer\": \"chat-module__Ix5oLa__chatContainer\",\n  \"chatMain\": \"chat-module__Ix5oLa__chatMain\",\n  \"inputArea\": \"chat-module__Ix5oLa__inputArea\",\n  \"inputBox\": \"chat-module__Ix5oLa__inputBox\",\n  \"mainContent\": \"chat-module__Ix5oLa__mainContent\",\n  \"messageContent\": \"chat-module__Ix5oLa__messageContent\",\n  \"messages\": \"chat-module__Ix5oLa__messages\",\n  \"modelSelectorContainer\": \"chat-module__Ix5oLa__modelSelectorContainer\",\n  \"placeholderContent\": \"chat-module__Ix5oLa__placeholderContent\",\n  \"sendButton\": \"chat-module__Ix5oLa__sendButton\",\n  \"userMessage\": \"chat-module__Ix5oLa__userMessage\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA"}}, {"offset": {"line": 28, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/AI/MetaRAG/metarag/frontend/src/app/components/TypeWriter.tsx"], "sourcesContent": ["\"use client\";\nimport React, { useState, useEffect, useCallback } from 'react';\n\ninterface TypeWriterProps {\n  text: string;\n  speed?: number; // 基础打字速度（毫秒）\n  className?: string;\n  onComplete?: () => void;\n  allowSkip?: boolean; // 是否允许点击跳过\n}\n\nexport default function TypeWriter({\n  text,\n  speed = 30,\n  className = '',\n  onComplete,\n  allowSkip = true\n}: TypeWriterProps) {\n  const [displayedText, setDisplayedText] = useState('');\n  const [currentIndex, setCurrentIndex] = useState(0);\n  const [isSkipped, setIsSkipped] = useState(false);\n\n  useEffect(() => {\n    // 重置状态当文本改变时\n    setDisplayedText('');\n    setCurrentIndex(0);\n    setIsSkipped(false);\n  }, [text]);\n\n  const handleSkip = useCallback(() => {\n    if (allowSkip && currentIndex < text.length) {\n      setIsSkipped(true);\n      setDisplayedText(text);\n      setCurrentIndex(text.length);\n    }\n  }, [allowSkip, currentIndex, text]);\n\n  const getCharDelay = useCallback((char: string, index: number) => {\n    // 为不同字符类型设置不同的延迟，使打字更自然\n    if (char === ' ') return speed * 0.5; // 空格快一些\n    if (char === '，' || char === '。' || char === '！' || char === '？') return speed * 2; // 标点符号慢一些\n    if (char === '\\n') return speed * 1.5; // 换行稍慢\n\n    // 添加轻微的随机变化使打字更自然\n    const randomFactor = 0.8 + Math.random() * 0.4; // 0.8 到 1.2 的随机因子\n    return speed * randomFactor;\n  }, [speed]);\n\n  useEffect(() => {\n    if (isSkipped) {\n      // 如果被跳过，立即完成\n      if (onComplete) {\n        const timer = setTimeout(() => {\n          onComplete();\n        }, 100);\n        return () => clearTimeout(timer);\n      }\n      return;\n    }\n\n    if (currentIndex < text.length) {\n      const char = text[currentIndex];\n      const delay = getCharDelay(char, currentIndex);\n\n      const timer = setTimeout(() => {\n        setDisplayedText(prev => prev + char);\n        setCurrentIndex(prev => prev + 1);\n      }, delay);\n\n      return () => clearTimeout(timer);\n    } else if (currentIndex === text.length && onComplete) {\n      // 打字完成后稍等一下再调用回调，让用户看到完整文本\n      const timer = setTimeout(() => {\n        onComplete();\n      }, 500);\n      return () => clearTimeout(timer);\n    }\n  }, [currentIndex, text, getCharDelay, onComplete, isSkipped]);\n\n  return (\n    <span\n      className={className}\n      onClick={handleSkip}\n      style={{\n        cursor: allowSkip && currentIndex < text.length ? 'pointer' : 'default',\n        userSelect: 'text'\n      }}\n      title={allowSkip && currentIndex < text.length ? '点击跳过打字动画' : undefined}\n    >\n      {displayedText}\n      {currentIndex < text.length && !isSkipped && (\n        <span className=\"typewriter-cursor\">|</span>\n      )}\n    </span>\n  );\n}\n"], "names": [], "mappings": ";;;;;AACA;AADA;;;AAWe,SAAS,WAAW,EACjC,IAAI,EACJ,QAAQ,EAAE,EACV,YAAY,EAAE,EACd,UAAU,EACV,YAAY,IAAI,EACA;IAChB,MAAM,CAAC,eAAe,iBAAiB,GAAG,IAAA,iNAAQ,EAAC;IACnD,MAAM,CAAC,cAAc,gBAAgB,GAAG,IAAA,iNAAQ,EAAC;IACjD,MAAM,CAAC,WAAW,aAAa,GAAG,IAAA,iNAAQ,EAAC;IAE3C,IAAA,kNAAS,EAAC;QACR,aAAa;QACb,iBAAiB;QACjB,gBAAgB;QAChB,aAAa;IACf,GAAG;QAAC;KAAK;IAET,MAAM,aAAa,IAAA,oNAAW,EAAC;QAC7B,IAAI,aAAa,eAAe,KAAK,MAAM,EAAE;YAC3C,aAAa;YACb,iBAAiB;YACjB,gBAAgB,KAAK,MAAM;QAC7B;IACF,GAAG;QAAC;QAAW;QAAc;KAAK;IAElC,MAAM,eAAe,IAAA,oNAAW,EAAC,CAAC,MAAc;QAC9C,wBAAwB;QACxB,IAAI,SAAS,KAAK,OAAO,QAAQ,KAAK,QAAQ;QAC9C,IAAI,SAAS,OAAO,SAAS,OAAO,SAAS,OAAO,SAAS,KAAK,OAAO,QAAQ,GAAG,UAAU;QAC9F,IAAI,SAAS,MAAM,OAAO,QAAQ,KAAK,OAAO;QAE9C,kBAAkB;QAClB,MAAM,eAAe,MAAM,KAAK,MAAM,KAAK,KAAK,kBAAkB;QAClE,OAAO,QAAQ;IACjB,GAAG;QAAC;KAAM;IAEV,IAAA,kNAAS,EAAC;QACR,IAAI,WAAW;YACb,aAAa;YACb,IAAI,YAAY;gBACd,MAAM,QAAQ,WAAW;oBACvB;gBACF,GAAG;gBACH,OAAO,IAAM,aAAa;YAC5B;YACA;QACF;QAEA,IAAI,eAAe,KAAK,MAAM,EAAE;YAC9B,MAAM,OAAO,IAAI,CAAC,aAAa;YAC/B,MAAM,QAAQ,aAAa,MAAM;YAEjC,MAAM,QAAQ,WAAW;gBACvB,iBAAiB,CAAA,OAAQ,OAAO;gBAChC,gBAAgB,CAAA,OAAQ,OAAO;YACjC,GAAG;YAEH,OAAO,IAAM,aAAa;QAC5B,OAAO,IAAI,iBAAiB,KAAK,MAAM,IAAI,YAAY;YACrD,2BAA2B;YAC3B,MAAM,QAAQ,WAAW;gBACvB;YACF,GAAG;YACH,OAAO,IAAM,aAAa;QAC5B;IACF,GAAG;QAAC;QAAc;QAAM;QAAc;QAAY;KAAU;IAE5D,qBACE,8OAAC;QACC,WAAW;QACX,SAAS;QACT,OAAO;YACL,QAAQ,aAAa,eAAe,KAAK,MAAM,GAAG,YAAY;YAC9D,YAAY;QACd;QACA,OAAO,aAAa,eAAe,KAAK,MAAM,GAAG,aAAa;;YAE7D;YACA,eAAe,KAAK,MAAM,IAAI,CAAC,2BAC9B,8OAAC;gBAAK,WAAU;0BAAoB;;;;;;;;;;;;AAI5C", "debugId": null}}, {"offset": {"line": 132, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/app/components/Sidebar.module.css [app-ssr] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"active\": \"Sidebar-module__JvW8nW__active\",\n  \"icon\": \"Sidebar-module__JvW8nW__icon\",\n  \"menuItem\": \"Sidebar-module__JvW8nW__menuItem\",\n  \"menuItems\": \"Sidebar-module__JvW8nW__menuItems\",\n  \"sidebar\": \"Sidebar-module__JvW8nW__sidebar\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA"}}, {"offset": {"line": 143, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/AI/MetaRAG/metarag/frontend/src/app/components/Sidebar.tsx"], "sourcesContent": ["\"use client\";\nimport React, { useState } from 'react';\nimport styles from './Sidebar.module.css';\n\ninterface SidebarProps {\n  activeTab: string;\n  onTabChange: (tab: string) => void;\n}\n\nconst Sidebar: React.FC<SidebarProps> = ({ activeTab, onTabChange }) => {\n  const menuItems = [\n    {\n      id: 'chat',\n      icon: (\n        <svg width=\"18\" height=\"18\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\n          <path d=\"M20 2H4c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h4l4 4 4-4h4c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zm-2 12H6v-2h12v2zm0-3H6V9h12v2zm0-3H6V6h12v2z\"/>\n        </svg>\n      ),\n      title: '对话历史',\n      tooltip: '查看对话历史'\n    },\n    {\n      id: 'code-review',\n      icon: (\n        <svg width=\"18\" height=\"18\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\n          <path d=\"M9.4 16.6L4.8 12l4.6-4.6L8 6l-6 6 6 6 1.4-1.4zm5.2 0L19.2 12l-4.6-4.6L16 6l6 6-6 6-1.4-1.4z\"/>\n        </svg>\n      ),\n      title: 'Code Review Assistant',\n      tooltip: '代码审查助手'\n    },\n    {\n      id: 'db-assistant',\n      icon: (\n        <svg width=\"18\" height=\"18\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\n          <path d=\"M12 3C7.58 3 4 4.79 4 7s3.58 4 8 4 8-1.79 8-4-3.58-4-8-4zM4 9v3c0 2.21 3.58 4 8 4s8-1.79 8-4V9c0 2.21-3.58 4-8 4s-8-1.79-8-4zm0 5v3c0 2.21 3.58 4 8 4s8-1.79 8-4v-3c0 2.21-3.58 4-8 4s-8-1.79-8-4z\"/>\n        </svg>\n      ),\n      title: 'DB Assistant',\n      tooltip: '数据库助手'\n    },\n    {\n      id: 'settings',\n      icon: (\n        <svg width=\"18\" height=\"18\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\n          <path d=\"M19.14,12.94c0.04-0.3,0.06-0.61,0.06-0.94c0-0.32-0.02-0.64-0.07-0.94l2.03-1.58c0.18-0.14,0.23-0.41,0.12-0.61 l-1.92-3.32c-0.12-0.22-0.37-0.29-0.59-0.22l-2.39,0.96c-0.5-0.38-1.03-0.7-1.62-0.94L14.4,2.81c-0.04-0.24-0.24-0.41-0.48-0.41 h-3.84c-0.24,0-0.43,0.17-0.47,0.41L9.25,5.35C8.66,5.59,8.12,5.92,7.63,6.29L5.24,5.33c-0.22-0.08-0.47,0-0.59,0.22L2.74,8.87 C2.62,9.08,2.66,9.34,2.86,9.48l2.03,1.58C4.84,11.36,4.8,11.69,4.8,12s0.02,0.64,0.07,0.94l-2.03,1.58 c-0.18,0.14-0.23,0.41-0.12,0.61l1.92,3.32c0.12,0.22,0.37,0.29,0.59,0.22l2.39-0.96c0.5,0.38,1.03,0.7,1.62,0.94l0.36,2.54 c0.05,0.24,0.24,0.41,0.48,0.41h3.84c0.24,0,0.44-0.17,0.47-0.41l0.36-2.54c0.59-0.24,1.13-0.56,1.62-0.94l2.39,0.96 c0.22,0.08,0.47,0,0.59-0.22l1.92-3.32c0.12-0.22,0.07-0.47-0.12-0.61L19.14,12.94z M12,15.6c-1.98,0-3.6-1.62-3.6-3.6 s1.62-3.6,3.6-3.6s3.6,1.62,3.6,3.6S13.98,15.6,12,15.6z\"/>\n        </svg>\n      ),\n      title: '设置',\n      tooltip: '系统设置'\n    }\n  ];\n\n  return (\n    <div className={styles.sidebar}>\n      <div className={styles.menuItems}>\n        {menuItems.map((item) => (\n          <button\n            key={item.id}\n            className={`${styles.menuItem} ${activeTab === item.id ? styles.active : ''}`}\n            onClick={() => onTabChange(item.id)}\n            title={item.tooltip}\n          >\n            <div className={styles.icon}>\n              {item.icon}\n            </div>\n          </button>\n        ))}\n      </div>\n    </div>\n  );\n};\n\nexport default Sidebar;\n"], "names": [], "mappings": ";;;;;AAEA;AAFA;;;AASA,MAAM,UAAkC,CAAC,EAAE,SAAS,EAAE,WAAW,EAAE;IACjE,MAAM,YAAY;QAChB;YACE,IAAI;YACJ,oBACE,8OAAC;gBAAI,OAAM;gBAAK,QAAO;gBAAK,SAAQ;gBAAY,MAAK;0BACnD,cAAA,8OAAC;oBAAK,GAAE;;;;;;;;;;;YAGZ,OAAO;YACP,SAAS;QACX;QACA;YACE,IAAI;YACJ,oBACE,8OAAC;gBAAI,OAAM;gBAAK,QAAO;gBAAK,SAAQ;gBAAY,MAAK;0BACnD,cAAA,8OAAC;oBAAK,GAAE;;;;;;;;;;;YAGZ,OAAO;YACP,SAAS;QACX;QACA;YACE,IAAI;YACJ,oBACE,8OAAC;gBAAI,OAAM;gBAAK,QAAO;gBAAK,SAAQ;gBAAY,MAAK;0BACnD,cAAA,8OAAC;oBAAK,GAAE;;;;;;;;;;;YAGZ,OAAO;YACP,SAAS;QACX;QACA;YACE,IAAI;YACJ,oBACE,8OAAC;gBAAI,OAAM;gBAAK,QAAO;gBAAK,SAAQ;gBAAY,MAAK;0BACnD,cAAA,8OAAC;oBAAK,GAAE;;;;;;;;;;;YAGZ,OAAO;YACP,SAAS;QACX;KACD;IAED,qBACE,8OAAC;QAAI,WAAW,0JAAM,CAAC,OAAO;kBAC5B,cAAA,8OAAC;YAAI,WAAW,0JAAM,CAAC,SAAS;sBAC7B,UAAU,GAAG,CAAC,CAAC,qBACd,8OAAC;oBAEC,WAAW,GAAG,0JAAM,CAAC,QAAQ,CAAC,CAAC,EAAE,cAAc,KAAK,EAAE,GAAG,0JAAM,CAAC,MAAM,GAAG,IAAI;oBAC7E,SAAS,IAAM,YAAY,KAAK,EAAE;oBAClC,OAAO,KAAK,OAAO;8BAEnB,cAAA,8OAAC;wBAAI,WAAW,0JAAM,CAAC,IAAI;kCACxB,KAAK,IAAI;;;;;;mBANP,KAAK,EAAE;;;;;;;;;;;;;;;AAaxB;uCAEe", "debugId": null}}, {"offset": {"line": 279, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/app/components/ModelSelector.module.css [app-ssr] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"chevron\": \"ModelSelector-module__0aG-aG__chevron\",\n  \"chevronUp\": \"ModelSelector-module__0aG-aG__chevronUp\",\n  \"dropdown\": \"ModelSelector-module__0aG-aG__dropdown\",\n  \"dropdownHeader\": \"ModelSelector-module__0aG-aG__dropdownHeader\",\n  \"modelItem\": \"ModelSelector-module__0aG-aG__modelItem\",\n  \"modelItemName\": \"ModelSelector-module__0aG-aG__modelItemName\",\n  \"modelItemTooltip\": \"ModelSelector-module__0aG-aG__modelItemTooltip\",\n  \"modelItemTooltipText\": \"ModelSelector-module__0aG-aG__modelItemTooltipText\",\n  \"modelList\": \"ModelSelector-module__0aG-aG__modelList\",\n  \"modelName\": \"ModelSelector-module__0aG-aG__modelName\",\n  \"modelSelector\": \"ModelSelector-module__0aG-aG__modelSelector\",\n  \"selected\": \"ModelSelector-module__0aG-aG__selected\",\n  \"selectorButton\": \"ModelSelector-module__0aG-aG__selectorButton\",\n  \"tooltipArrow\": \"ModelSelector-module__0aG-aG__tooltipArrow\",\n  \"visible\": \"ModelSelector-module__0aG-aG__visible\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA"}}, {"offset": {"line": 300, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/AI/MetaRAG/metarag/frontend/src/app/components/ModelSelector.tsx"], "sourcesContent": ["\"use client\";\nimport React, { useState, useEffect, useRef } from 'react';\nimport styles from './ModelSelector.module.css';\n\ninterface ModelSelectorProps {\n  selectedModel: string;\n  onModelChange: (model: string) => void;\n}\n\n\n\nconst ModelSelector: React.FC<ModelSelectorProps> = ({ selectedModel, onModelChange }) => {\n  const [models, setModels] = useState<string[]>([]);\n  const [isOpen, setIsOpen] = useState(false);\n  const [loading, setLoading] = useState(true);\n  const [hoveredModel, setHoveredModel] = useState<string | null>(null);\n  const [tooltipPosition, setTooltipPosition] = useState({ x: 0, y: 0 });\n  const dropdownRef = useRef<HTMLDivElement>(null);\n\n  // 将模型名称首字母大写\n  const capitalizeModelName = (name: string) => {\n    return name.charAt(0).toUpperCase() + name.slice(1);\n  };\n\n  // 处理鼠标进入事件，计算tooltip位置\n  const handleMouseEnter = (model: string, event: React.MouseEvent) => {\n    const rect = event.currentTarget.getBoundingClientRect();\n    setTooltipPosition({\n      x: rect.right + 10, // 显示在右侧\n      y: rect.top + rect.height / 2\n    });\n    setHoveredModel(model);\n  };\n\n  // 处理鼠标离开事件\n  const handleMouseLeave = () => {\n    setHoveredModel(null);\n  };\n\n  useEffect(() => {\n    fetchModels();\n  }, []);\n\n  useEffect(() => {\n    const handleClickOutside = (event: MouseEvent) => {\n      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {\n        setIsOpen(false);\n      }\n    };\n\n    document.addEventListener('mousedown', handleClickOutside);\n    return () => {\n      document.removeEventListener('mousedown', handleClickOutside);\n    };\n  }, []);\n\n  const fetchModels = async () => {\n    try {\n      setLoading(true);\n      const response = await fetch('/api/models');\n      if (response.ok) {\n        const data = await response.json();\n        // 过滤掉含有\"embed\"的模型，并按字母顺序排列\n        const filteredModels = (data.models || [])\n          .filter((model: string) => !model.toLowerCase().includes('embed'))\n          .sort((a: string, b: string) => a.localeCompare(b));\n        setModels(filteredModels);\n      } else {\n        // 如果API不可用，使用默认模型\n        setModels(['qwen3:8b']);\n      }\n    } catch (error) {\n      console.error('Failed to fetch models:', error);\n      // 如果获取失败，使用默认模型\n      setModels(['qwen3:8b']);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleModelSelect = (model: string) => {\n    onModelChange(model);\n    setIsOpen(false);\n  };\n\n  const displayName = selectedModel || 'qwen3:8b';\n\n  return (\n    <div className={styles.modelSelector} ref={dropdownRef}>\n      <button\n        className={styles.selectorButton}\n        onClick={() => setIsOpen(!isOpen)}\n        disabled={loading}\n      >\n        <span className={styles.modelName}>\n          {loading ? '加载中...' : capitalizeModelName(displayName)}\n        </span>\n        <svg\n          className={`${styles.chevron} ${isOpen ? styles.chevronUp : ''}`}\n          width=\"16\"\n          height=\"16\"\n          viewBox=\"0 0 24 24\"\n          fill=\"currentColor\"\n        >\n          <path d=\"M7 10l5 5 5-5z\"/>\n        </svg>\n      </button>\n\n      {isOpen && !loading && (\n        <div className={styles.dropdown}>\n          <div className={styles.dropdownHeader}>\n            <span>选择AI模型</span>\n          </div>\n          <div className={styles.modelList}>\n            {models.map((model) => (\n              <div\n                key={model}\n                className={styles.modelItemTooltip}\n                onMouseEnter={(e) => handleMouseEnter(model, e)}\n                onMouseLeave={handleMouseLeave}\n              >\n                <button\n                  className={`${styles.modelItem} ${selectedModel === model ? styles.selected : ''}`}\n                  onClick={() => handleModelSelect(model)}\n                >\n                  <span className={styles.modelItemName}>{capitalizeModelName(model)}</span>\n                  {selectedModel === model && (\n                    <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\n                      <path d=\"M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z\"/>\n                    </svg>\n                  )}\n                </button>\n              </div>\n            ))}\n          </div>\n\n          {/* 全局tooltip */}\n          {hoveredModel && (\n            <div\n              className={`${styles.modelItemTooltipText} ${styles.visible}`}\n              style={{\n                left: `${tooltipPosition.x}px`,\n                top: `${tooltipPosition.y}px`,\n                transform: 'translate(0, -50%)'\n              }}\n            >\n              {capitalizeModelName(hoveredModel)}\n              <div className={styles.tooltipArrow}></div>\n            </div>\n          )}\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default ModelSelector;\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAFA;;;;AAWA,MAAM,gBAA8C,CAAC,EAAE,aAAa,EAAE,aAAa,EAAE;IACnF,MAAM,CAAC,QAAQ,UAAU,GAAG,IAAA,iNAAQ,EAAW,EAAE;IACjD,MAAM,CAAC,QAAQ,UAAU,GAAG,IAAA,iNAAQ,EAAC;IACrC,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,iNAAQ,EAAC;IACvC,MAAM,CAAC,cAAc,gBAAgB,GAAG,IAAA,iNAAQ,EAAgB;IAChE,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,IAAA,iNAAQ,EAAC;QAAE,GAAG;QAAG,GAAG;IAAE;IACpE,MAAM,cAAc,IAAA,+MAAM,EAAiB;IAE3C,aAAa;IACb,MAAM,sBAAsB,CAAC;QAC3B,OAAO,KAAK,MAAM,CAAC,GAAG,WAAW,KAAK,KAAK,KAAK,CAAC;IACnD;IAEA,uBAAuB;IACvB,MAAM,mBAAmB,CAAC,OAAe;QACvC,MAAM,OAAO,MAAM,aAAa,CAAC,qBAAqB;QACtD,mBAAmB;YACjB,GAAG,KAAK,KAAK,GAAG;YAChB,GAAG,KAAK,GAAG,GAAG,KAAK,MAAM,GAAG;QAC9B;QACA,gBAAgB;IAClB;IAEA,WAAW;IACX,MAAM,mBAAmB;QACvB,gBAAgB;IAClB;IAEA,IAAA,kNAAS,EAAC;QACR;IACF,GAAG,EAAE;IAEL,IAAA,kNAAS,EAAC;QACR,MAAM,qBAAqB,CAAC;YAC1B,IAAI,YAAY,OAAO,IAAI,CAAC,YAAY,OAAO,CAAC,QAAQ,CAAC,MAAM,MAAM,GAAW;gBAC9E,UAAU;YACZ;QACF;QAEA,SAAS,gBAAgB,CAAC,aAAa;QACvC,OAAO;YACL,SAAS,mBAAmB,CAAC,aAAa;QAC5C;IACF,GAAG,EAAE;IAEL,MAAM,cAAc;QAClB,IAAI;YACF,WAAW;YACX,MAAM,WAAW,MAAM,MAAM;YAC7B,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,2BAA2B;gBAC3B,MAAM,iBAAiB,CAAC,KAAK,MAAM,IAAI,EAAE,EACtC,MAAM,CAAC,CAAC,QAAkB,CAAC,MAAM,WAAW,GAAG,QAAQ,CAAC,UACxD,IAAI,CAAC,CAAC,GAAW,IAAc,EAAE,aAAa,CAAC;gBAClD,UAAU;YACZ,OAAO;gBACL,kBAAkB;gBAClB,UAAU;oBAAC;iBAAW;YACxB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;YACzC,gBAAgB;YAChB,UAAU;gBAAC;aAAW;QACxB,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,oBAAoB,CAAC;QACzB,cAAc;QACd,UAAU;IACZ;IAEA,MAAM,cAAc,iBAAiB;IAErC,qBACE,8OAAC;QAAI,WAAW,gKAAM,CAAC,aAAa;QAAE,KAAK;;0BACzC,8OAAC;gBACC,WAAW,gKAAM,CAAC,cAAc;gBAChC,SAAS,IAAM,UAAU,CAAC;gBAC1B,UAAU;;kCAEV,8OAAC;wBAAK,WAAW,gKAAM,CAAC,SAAS;kCAC9B,UAAU,WAAW,oBAAoB;;;;;;kCAE5C,8OAAC;wBACC,WAAW,GAAG,gKAAM,CAAC,OAAO,CAAC,CAAC,EAAE,SAAS,gKAAM,CAAC,SAAS,GAAG,IAAI;wBAChE,OAAM;wBACN,QAAO;wBACP,SAAQ;wBACR,MAAK;kCAEL,cAAA,8OAAC;4BAAK,GAAE;;;;;;;;;;;;;;;;;YAIX,UAAU,CAAC,yBACV,8OAAC;gBAAI,WAAW,gKAAM,CAAC,QAAQ;;kCAC7B,8OAAC;wBAAI,WAAW,gKAAM,CAAC,cAAc;kCACnC,cAAA,8OAAC;sCAAK;;;;;;;;;;;kCAER,8OAAC;wBAAI,WAAW,gKAAM,CAAC,SAAS;kCAC7B,OAAO,GAAG,CAAC,CAAC,sBACX,8OAAC;gCAEC,WAAW,gKAAM,CAAC,gBAAgB;gCAClC,cAAc,CAAC,IAAM,iBAAiB,OAAO;gCAC7C,cAAc;0CAEd,cAAA,8OAAC;oCACC,WAAW,GAAG,gKAAM,CAAC,SAAS,CAAC,CAAC,EAAE,kBAAkB,QAAQ,gKAAM,CAAC,QAAQ,GAAG,IAAI;oCAClF,SAAS,IAAM,kBAAkB;;sDAEjC,8OAAC;4CAAK,WAAW,gKAAM,CAAC,aAAa;sDAAG,oBAAoB;;;;;;wCAC3D,kBAAkB,uBACjB,8OAAC;4CAAI,OAAM;4CAAK,QAAO;4CAAK,SAAQ;4CAAY,MAAK;sDACnD,cAAA,8OAAC;gDAAK,GAAE;;;;;;;;;;;;;;;;;+BAZT;;;;;;;;;;oBAqBV,8BACC,8OAAC;wBACC,WAAW,GAAG,gKAAM,CAAC,oBAAoB,CAAC,CAAC,EAAE,gKAAM,CAAC,OAAO,EAAE;wBAC7D,OAAO;4BACL,MAAM,GAAG,gBAAgB,CAAC,CAAC,EAAE,CAAC;4BAC9B,KAAK,GAAG,gBAAgB,CAAC,CAAC,EAAE,CAAC;4BAC7B,WAAW;wBACb;;4BAEC,oBAAoB;0CACrB,8OAAC;gCAAI,WAAW,gKAAM,CAAC,YAAY;;;;;;;;;;;;;;;;;;;;;;;;AAOjD;uCAEe", "debugId": null}}, {"offset": {"line": 530, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/app/components/DocumentsSection.module.css [app-ssr] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"documentContent\": \"DocumentsSection-module__2JF54W__documentContent\",\n  \"documentItem\": \"DocumentsSection-module__2JF54W__documentItem\",\n  \"documentMetadata\": \"DocumentsSection-module__2JF54W__documentMetadata\",\n  \"documentTitle\": \"DocumentsSection-module__2JF54W__documentTitle\",\n  \"documentsContent\": \"DocumentsSection-module__2JF54W__documentsContent\",\n  \"documentsHeader\": \"DocumentsSection-module__2JF54W__documentsHeader\",\n  \"documentsSection\": \"DocumentsSection-module__2JF54W__documentsSection\",\n  \"documentsTitle\": \"DocumentsSection-module__2JF54W__documentsTitle\",\n  \"expandIcon\": \"DocumentsSection-module__2JF54W__expandIcon\",\n  \"expanded\": \"DocumentsSection-module__2JF54W__expanded\",\n  \"noDocuments\": \"DocumentsSection-module__2JF54W__noDocuments\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA"}}, {"offset": {"line": 547, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/AI/MetaRAG/metarag/frontend/src/app/components/DocumentsSection.tsx"], "sourcesContent": ["\"use client\";\nimport React, { useState } from 'react';\nimport styles from './DocumentsSection.module.css';\n\ninterface Document {\n  title: string;\n  page_content: string;\n  metadata: Record<string, any>;\n}\n\ninterface DocumentsSectionProps {\n  documents: Document[];\n}\n\nconst DocumentsSection: React.FC<DocumentsSectionProps> = ({ documents }) => {\n  const [isExpanded, setIsExpanded] = useState(false);\n\n  if (!documents || documents.length === 0) {\n    return (\n      <div className={styles.documentsSection}>\n        <div className={styles.noDocuments}>\n          This answer is based on the AI's own knowledge. No relevant documents in the knowledge base.\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className={styles.documentsSection}>\n      <div \n        className={styles.documentsHeader}\n        onClick={() => setIsExpanded(!isExpanded)}\n      >\n        <span className={styles.documentsTitle}>\n          📚 Related Documents ({documents.length})\n        </span>\n        <span className={`${styles.expandIcon} ${isExpanded ? styles.expanded : ''}`}>\n          ▼\n        </span>\n      </div>\n      \n      {isExpanded && (\n        <div className={styles.documentsContent}>\n          {documents.map((doc, index) => (\n            <div key={index} className={styles.documentItem}>\n              <div className={styles.documentTitle}>\n                {doc.title || `Document ${index + 1}`}\n              </div>\n              <div className={styles.documentContent}>\n                {doc.page_content}\n              </div>\n              {doc.metadata && Object.keys(doc.metadata).length > 0 && (\n                <div className={styles.documentMetadata}>\n                  <strong>Metadata:</strong>\n                  <pre>{JSON.stringify(doc.metadata, null, 2)}</pre>\n                </div>\n              )}\n            </div>\n          ))}\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default DocumentsSection;\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAFA;;;;AAcA,MAAM,mBAAoD,CAAC,EAAE,SAAS,EAAE;IACtE,MAAM,CAAC,YAAY,cAAc,GAAG,IAAA,iNAAQ,EAAC;IAE7C,IAAI,CAAC,aAAa,UAAU,MAAM,KAAK,GAAG;QACxC,qBACE,8OAAC;YAAI,WAAW,mKAAM,CAAC,gBAAgB;sBACrC,cAAA,8OAAC;gBAAI,WAAW,mKAAM,CAAC,WAAW;0BAAE;;;;;;;;;;;IAK1C;IAEA,qBACE,8OAAC;QAAI,WAAW,mKAAM,CAAC,gBAAgB;;0BACrC,8OAAC;gBACC,WAAW,mKAAM,CAAC,eAAe;gBACjC,SAAS,IAAM,cAAc,CAAC;;kCAE9B,8OAAC;wBAAK,WAAW,mKAAM,CAAC,cAAc;;4BAAE;4BACf,UAAU,MAAM;4BAAC;;;;;;;kCAE1C,8OAAC;wBAAK,WAAW,GAAG,mKAAM,CAAC,UAAU,CAAC,CAAC,EAAE,aAAa,mKAAM,CAAC,QAAQ,GAAG,IAAI;kCAAE;;;;;;;;;;;;YAK/E,4BACC,8OAAC;gBAAI,WAAW,mKAAM,CAAC,gBAAgB;0BACpC,UAAU,GAAG,CAAC,CAAC,KAAK,sBACnB,8OAAC;wBAAgB,WAAW,mKAAM,CAAC,YAAY;;0CAC7C,8OAAC;gCAAI,WAAW,mKAAM,CAAC,aAAa;0CACjC,IAAI,KAAK,IAAI,CAAC,SAAS,EAAE,QAAQ,GAAG;;;;;;0CAEvC,8OAAC;gCAAI,WAAW,mKAAM,CAAC,eAAe;0CACnC,IAAI,YAAY;;;;;;4BAElB,IAAI,QAAQ,IAAI,OAAO,IAAI,CAAC,IAAI,QAAQ,EAAE,MAAM,GAAG,mBAClD,8OAAC;gCAAI,WAAW,mKAAM,CAAC,gBAAgB;;kDACrC,8OAAC;kDAAO;;;;;;kDACR,8OAAC;kDAAK,KAAK,SAAS,CAAC,IAAI,QAAQ,EAAE,MAAM;;;;;;;;;;;;;uBAVrC;;;;;;;;;;;;;;;;AAmBtB;uCAEe", "debugId": null}}, {"offset": {"line": 677, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/AI/MetaRAG/metarag/frontend/src/app/ChatPage.tsx"], "sourcesContent": ["\"use client\";\r\nimport React, { useState, useRef, useEffect } from 'react';\r\nimport styles from './chat.module.css';\r\nimport TypeWriter from './components/TypeWriter';\r\nimport Sidebar from './components/Sidebar';\r\nimport ModelSelector from './components/ModelSelector';\r\nimport DocumentsSection from './components/DocumentsSection';\r\n\r\ninterface Document {\r\n  title: string;\r\n  page_content: string;\r\n  metadata: Record<string, any>;\r\n}\r\n\r\ninterface Message {\r\n  role: 'user' | 'assistant';\r\n  content: string;\r\n  id: string;\r\n  isTyping?: boolean;\r\n  documents?: Document[];\r\n}\r\n\r\nconst initialMessages: Message[] = [\r\n  {\r\n    role: 'assistant',\r\n    content: '你好！我是AI助手，有什么可以帮您？',\r\n    id: 'initial-1',\r\n    isTyping: true\r\n  },\r\n];\r\n\r\nexport default function ChatPage() {\r\n  const [messages, setMessages] = useState<Message[]>(initialMessages);\r\n  const [input, setInput] = useState('');\r\n  const [activeTab, setActiveTab] = useState('chat');\r\n  const [selectedModel, setSelectedModel] = useState('qwen3:8b');\r\n  const messagesEndRef = useRef<HTMLDivElement>(null);\r\n\r\n  useEffect(() => {\r\n    const fetchCurrentModel = async () => {\r\n      try {\r\n        const response = await fetch('/api/current-model');\r\n        const data = await response.json();\r\n        if (data.current_model) {\r\n          setSelectedModel(data.current_model);\r\n        }\r\n      } catch (error) {\r\n        console.error('Error fetching current model:', error);\r\n      }\r\n    };\r\n\r\n    fetchCurrentModel();\r\n  }, []);\r\n\r\n  // avoid auto-scrolling on initial mount; only scroll when messages update after mount\r\n  const didMountRef = useRef(false);\r\n  useEffect(() => {\r\n    if (!didMountRef.current) {\r\n      didMountRef.current = true;\r\n      return;\r\n    }\r\n    // 使用页面滚动到底部，而不是元素内部滚动\r\n    setTimeout(() => {\r\n      window.scrollTo({\r\n        top: document.documentElement.scrollHeight,\r\n        behavior: 'smooth'\r\n      });\r\n    }, 100); // 小延迟确保DOM更新完成\r\n  }, [messages]);\r\n\r\n  const generateId = () => `msg-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;\r\n\r\n  const handleTypingComplete = (messageId: string) => {\r\n    setMessages(msgs =>\r\n      msgs.map(msg =>\r\n        msg.id === messageId ? { ...msg, isTyping: false } : msg\r\n      )\r\n    );\r\n  };\r\n\r\n  const handleSend = async () => {\r\n    if (!input.trim()) return;\r\n\r\n    const userMessage: Message = {\r\n      role: 'user',\r\n      content: input,\r\n      id: generateId(),\r\n      isTyping: false\r\n    };\r\n    const newMessages: Message[] = [...messages, userMessage];\r\n    setMessages(newMessages);\r\n    setInput('');\r\n\r\n    try {\r\n      const resp = await fetch('/api/chat', {\r\n        method: 'POST',\r\n        headers: { 'Content-Type': 'application/json' },\r\n        body: JSON.stringify({\r\n          question: input,\r\n          model: selectedModel, \r\n          messages: newMessages.map(m => ({\r\n            type: m.role === 'user' ? 'human' : 'ai',\r\n            content: m.content\r\n          }))\r\n        })\r\n      });\r\n      const data = await resp.json();\r\n      if (data && data.answer) {\r\n        const assistantMessage: Message = {\r\n          role: 'assistant',\r\n          content: data.answer,\r\n          id: generateId(),\r\n          isTyping: true,\r\n          documents: data.documents || []\r\n        };\r\n        setMessages(msgs => [...msgs, assistantMessage]);\r\n      }\r\n    } catch (e) {\r\n      const errorMessage: Message = {\r\n        role: 'assistant',\r\n        content: 'AI服务异常，请稍后重试。',\r\n        id: generateId(),\r\n        isTyping: true\r\n      };\r\n      setMessages(msgs => [...msgs, errorMessage]);\r\n    }\r\n  };\r\n\r\n  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {\r\n    if (e.key === 'Enter' && !e.shiftKey) {\r\n      e.preventDefault();\r\n      handleSend();\r\n    }\r\n  };\r\n\r\n  const handleTabChange = (tab: string) => {\r\n    setActiveTab(tab);\r\n    // 这里可以添加不同标签页的逻辑\r\n    console.log('切换到标签页:', tab);\r\n  };\r\n\r\n  const handleModelChange = async (model: string) => {\r\n    setSelectedModel(model);\r\n    console.log('Switching model to:', model);\r\n\r\n    try {\r\n      const response = await fetch('/api/switch-model', {\r\n        method: 'POST',\r\n        headers: { 'Content-Type': 'application/json' },\r\n        body: JSON.stringify({\r\n          model: model\r\n        })\r\n      });\r\n\r\n      const data = await response.json();\r\n      if (data.success) {\r\n        console.log(`Model switch successful: ${data.message}`);\r\n      } else {\r\n        console.error(`Model switch failed: ${data.message}`);\r\n      }\r\n    } catch (error) {\r\n      console.error('Model switch request failed:', error);\r\n    }\r\n  };\r\n\r\n  // 根据当前活动标签页渲染不同内容\r\n  const renderContent = () => {\r\n    switch (activeTab) {\r\n      case 'chat':\r\n        return (\r\n          <div className={styles.chatContainer}>\r\n            <div className={styles.chatMain}>\r\n              <div className={styles.messages}>\r\n                {messages.map((msg) => (\r\n                  <div\r\n                    key={msg.id}\r\n                    className={\r\n                      msg.role === 'user' ? styles.userMessage : styles.assistantMessage\r\n                    }\r\n                  >\r\n                    <div className={styles.messageContent}>\r\n                      {msg.role === 'assistant' && msg.isTyping ? (\r\n                        <TypeWriter\r\n                          text={msg.content}\r\n                          speed={30}\r\n                          allowSkip={true}\r\n                          onComplete={() => handleTypingComplete(msg.id)}\r\n                        />\r\n                      ) : (\r\n                        msg.content\r\n                      )}\r\n                    </div>\r\n                    {msg.role === 'assistant' && msg.documents && !msg.isTyping && (\r\n                      <DocumentsSection documents={msg.documents} />\r\n                    )}\r\n                  </div>\r\n                ))}\r\n                <div ref={messagesEndRef} />\r\n              </div>\r\n            </div>\r\n            <div className={styles.inputArea}>\r\n                <textarea\r\n                  className={styles.inputBox}\r\n                  value={input}\r\n                  onChange={e => setInput(e.target.value)}\r\n                  onKeyDown={handleKeyDown}\r\n                  placeholder=\"输入您的问题...\"\r\n                  rows={1}\r\n                />\r\n                <button className={styles.sendButton} onClick={handleSend} aria-label=\"发送\">\r\n                  <svg width=\"20\" height=\"20\" viewBox=\"0 0 20 20\" fill=\"currentColor\" xmlns=\"http://www.w3.org/2000/svg\"><path d=\"M8.99992 16V6.41407L5.70696 9.70704C5.31643 10.0976 4.68342 10.0976 4.29289 9.70704C3.90237 9.31652 3.90237 8.6835 4.29289 8.29298L9.29289 3.29298L9.36907 3.22462C9.76184 2.90427 10.3408 2.92686 10.707 3.29298L15.707 8.29298L15.7753 8.36915C16.0957 8.76192 16.0731 9.34092 15.707 9.70704C15.3408 10.0732 14.7618 10.0958 14.3691 9.7754L14.2929 9.70704L10.9999 6.41407V16C10.9999 16.5523 10.5522 17 9.99992 17C9.44764 17 8.99992 16.5523 8.99992 16Z\"></path></svg>\r\n                </button>\r\n            </div>\r\n          </div>\r\n        );\r\n      case 'code-review':\r\n        return (\r\n          <div className={styles.placeholderContent}>\r\n            <h2>Code Review Assistant</h2>\r\n            <p>代码审查助手功能正在开发中...</p>\r\n          </div>\r\n        );\r\n      case 'db-assistant':\r\n        return (\r\n          <div className={styles.placeholderContent}>\r\n            <h2>DB Assistant</h2>\r\n            <p>数据库助手功能正在开发中...</p>\r\n          </div>\r\n        );\r\n      case 'settings':\r\n        return (\r\n          <div className={styles.placeholderContent}>\r\n            <h2>设置</h2>\r\n            <p>系统设置功能正在开发中...</p>\r\n          </div>\r\n        );\r\n      default:\r\n        return null;\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className={styles.appContainer}>\r\n      <Sidebar activeTab={activeTab} onTabChange={handleTabChange} />\r\n\r\n      <div className={styles.modelSelectorContainer}>\r\n        <ModelSelector\r\n          selectedModel={selectedModel}\r\n          onModelChange={handleModelChange}\r\n        />\r\n      </div>\r\n\r\n      <div className={styles.mainContent}>\r\n        {renderContent()}\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;;AACA;AACA;AACA;AACA;AACA;AACA;AANA;;;;;;;;AAsBA,MAAM,kBAA6B;IACjC;QACE,MAAM;QACN,SAAS;QACT,IAAI;QACJ,UAAU;IACZ;CACD;AAEc,SAAS;IACtB,MAAM,CAAC,UAAU,YAAY,GAAG,IAAA,iNAAQ,EAAY;IACpD,MAAM,CAAC,OAAO,SAAS,GAAG,IAAA,iNAAQ,EAAC;IACnC,MAAM,CAAC,WAAW,aAAa,GAAG,IAAA,iNAAQ,EAAC;IAC3C,MAAM,CAAC,eAAe,iBAAiB,GAAG,IAAA,iNAAQ,EAAC;IACnD,MAAM,iBAAiB,IAAA,+MAAM,EAAiB;IAE9C,IAAA,kNAAS,EAAC;QACR,MAAM,oBAAoB;YACxB,IAAI;gBACF,MAAM,WAAW,MAAM,MAAM;gBAC7B,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,IAAI,KAAK,aAAa,EAAE;oBACtB,iBAAiB,KAAK,aAAa;gBACrC;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,iCAAiC;YACjD;QACF;QAEA;IACF,GAAG,EAAE;IAEL,sFAAsF;IACtF,MAAM,cAAc,IAAA,+MAAM,EAAC;IAC3B,IAAA,kNAAS,EAAC;QACR,IAAI,CAAC,YAAY,OAAO,EAAE;YACxB,YAAY,OAAO,GAAG;YACtB;QACF;QACA,sBAAsB;QACtB,WAAW;YACT,OAAO,QAAQ,CAAC;gBACd,KAAK,SAAS,eAAe,CAAC,YAAY;gBAC1C,UAAU;YACZ;QACF,GAAG,MAAM,eAAe;IAC1B,GAAG;QAAC;KAAS;IAEb,MAAM,aAAa,IAAM,CAAC,IAAI,EAAE,KAAK,GAAG,GAAG,CAAC,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,SAAS,CAAC,GAAG,KAAK;IAE3F,MAAM,uBAAuB,CAAC;QAC5B,YAAY,CAAA,OACV,KAAK,GAAG,CAAC,CAAA,MACP,IAAI,EAAE,KAAK,YAAY;oBAAE,GAAG,GAAG;oBAAE,UAAU;gBAAM,IAAI;IAG3D;IAEA,MAAM,aAAa;QACjB,IAAI,CAAC,MAAM,IAAI,IAAI;QAEnB,MAAM,cAAuB;YAC3B,MAAM;YACN,SAAS;YACT,IAAI;YACJ,UAAU;QACZ;QACA,MAAM,cAAyB;eAAI;YAAU;SAAY;QACzD,YAAY;QACZ,SAAS;QAET,IAAI;YACF,MAAM,OAAO,MAAM,MAAM,aAAa;gBACpC,QAAQ;gBACR,SAAS;oBAAE,gBAAgB;gBAAmB;gBAC9C,MAAM,KAAK,SAAS,CAAC;oBACnB,UAAU;oBACV,OAAO;oBACP,UAAU,YAAY,GAAG,CAAC,CAAA,IAAK,CAAC;4BAC9B,MAAM,EAAE,IAAI,KAAK,SAAS,UAAU;4BACpC,SAAS,EAAE,OAAO;wBACpB,CAAC;gBACH;YACF;YACA,MAAM,OAAO,MAAM,KAAK,IAAI;YAC5B,IAAI,QAAQ,KAAK,MAAM,EAAE;gBACvB,MAAM,mBAA4B;oBAChC,MAAM;oBACN,SAAS,KAAK,MAAM;oBACpB,IAAI;oBACJ,UAAU;oBACV,WAAW,KAAK,SAAS,IAAI,EAAE;gBACjC;gBACA,YAAY,CAAA,OAAQ;2BAAI;wBAAM;qBAAiB;YACjD;QACF,EAAE,OAAO,GAAG;YACV,MAAM,eAAwB;gBAC5B,MAAM;gBACN,SAAS;gBACT,IAAI;gBACJ,UAAU;YACZ;YACA,YAAY,CAAA,OAAQ;uBAAI;oBAAM;iBAAa;QAC7C;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,IAAI,EAAE,GAAG,KAAK,WAAW,CAAC,EAAE,QAAQ,EAAE;YACpC,EAAE,cAAc;YAChB;QACF;IACF;IAEA,MAAM,kBAAkB,CAAC;QACvB,aAAa;QACb,iBAAiB;QACjB,QAAQ,GAAG,CAAC,WAAW;IACzB;IAEA,MAAM,oBAAoB,OAAO;QAC/B,iBAAiB;QACjB,QAAQ,GAAG,CAAC,uBAAuB;QAEnC,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,qBAAqB;gBAChD,QAAQ;gBACR,SAAS;oBAAE,gBAAgB;gBAAmB;gBAC9C,MAAM,KAAK,SAAS,CAAC;oBACnB,OAAO;gBACT;YACF;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,IAAI,KAAK,OAAO,EAAE;gBAChB,QAAQ,GAAG,CAAC,CAAC,yBAAyB,EAAE,KAAK,OAAO,EAAE;YACxD,OAAO;gBACL,QAAQ,KAAK,CAAC,CAAC,qBAAqB,EAAE,KAAK,OAAO,EAAE;YACtD;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;QAChD;IACF;IAEA,kBAAkB;IAClB,MAAM,gBAAgB;QACpB,OAAQ;YACN,KAAK;gBACH,qBACE,8OAAC;oBAAI,WAAW,yIAAM,CAAC,aAAa;;sCAClC,8OAAC;4BAAI,WAAW,yIAAM,CAAC,QAAQ;sCAC7B,cAAA,8OAAC;gCAAI,WAAW,yIAAM,CAAC,QAAQ;;oCAC5B,SAAS,GAAG,CAAC,CAAC,oBACb,8OAAC;4CAEC,WACE,IAAI,IAAI,KAAK,SAAS,yIAAM,CAAC,WAAW,GAAG,yIAAM,CAAC,gBAAgB;;8DAGpE,8OAAC;oDAAI,WAAW,yIAAM,CAAC,cAAc;8DAClC,IAAI,IAAI,KAAK,eAAe,IAAI,QAAQ,iBACvC,8OAAC,kJAAU;wDACT,MAAM,IAAI,OAAO;wDACjB,OAAO;wDACP,WAAW;wDACX,YAAY,IAAM,qBAAqB,IAAI,EAAE;;;;;+DAG/C,IAAI,OAAO;;;;;;gDAGd,IAAI,IAAI,KAAK,eAAe,IAAI,SAAS,IAAI,CAAC,IAAI,QAAQ,kBACzD,8OAAC,wJAAgB;oDAAC,WAAW,IAAI,SAAS;;;;;;;2CAlBvC,IAAI,EAAE;;;;;kDAsBf,8OAAC;wCAAI,KAAK;;;;;;;;;;;;;;;;;sCAGd,8OAAC;4BAAI,WAAW,yIAAM,CAAC,SAAS;;8CAC5B,8OAAC;oCACC,WAAW,yIAAM,CAAC,QAAQ;oCAC1B,OAAO;oCACP,UAAU,CAAA,IAAK,SAAS,EAAE,MAAM,CAAC,KAAK;oCACtC,WAAW;oCACX,aAAY;oCACZ,MAAM;;;;;;8CAER,8OAAC;oCAAO,WAAW,yIAAM,CAAC,UAAU;oCAAE,SAAS;oCAAY,cAAW;8CACpE,cAAA,8OAAC;wCAAI,OAAM;wCAAK,QAAO;wCAAK,SAAQ;wCAAY,MAAK;wCAAe,OAAM;kDAA6B,cAAA,8OAAC;4CAAK,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAK3H,KAAK;gBACH,qBACE,8OAAC;oBAAI,WAAW,yIAAM,CAAC,kBAAkB;;sCACvC,8OAAC;sCAAG;;;;;;sCACJ,8OAAC;sCAAE;;;;;;;;;;;;YAGT,KAAK;gBACH,qBACE,8OAAC;oBAAI,WAAW,yIAAM,CAAC,kBAAkB;;sCACvC,8OAAC;sCAAG;;;;;;sCACJ,8OAAC;sCAAE;;;;;;;;;;;;YAGT,KAAK;gBACH,qBACE,8OAAC;oBAAI,WAAW,yIAAM,CAAC,kBAAkB;;sCACvC,8OAAC;sCAAG;;;;;;sCACJ,8OAAC;sCAAE;;;;;;;;;;;;YAGT;gBACE,OAAO;QACX;IACF;IAEA,qBACE,8OAAC;QAAI,WAAW,yIAAM,CAAC,YAAY;;0BACjC,8OAAC,+IAAO;gBAAC,WAAW;gBAAW,aAAa;;;;;;0BAE5C,8OAAC;gBAAI,WAAW,yIAAM,CAAC,sBAAsB;0BAC3C,cAAA,8OAAC,qJAAa;oBACZ,eAAe;oBACf,eAAe;;;;;;;;;;;0BAInB,8OAAC;gBAAI,WAAW,yIAAM,CAAC,WAAW;0BAC/B;;;;;;;;;;;;AAIT", "debugId": null}}, {"offset": {"line": 1076, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/AI/MetaRAG/metarag/frontend/src/app/page.tsx"], "sourcesContent": ["\"use client\";\nimport ChatPage from './ChatPage';\n\nexport default function Home() {\n  return <ChatPage />;\n}\n"], "names": [], "mappings": ";;;;;AACA;AADA;;;AAGe,SAAS;IACtB,qBAAO,8OAAC,kIAAQ;;;;;AAClB", "debugId": null}}, {"offset": {"line": 1096, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/AI/MetaRAG/metarag/frontend/node_modules/next/src/server/route-modules/app-page/module.compiled.js"], "sourcesContent": ["if (process.env.NEXT_RUNTIME === 'edge') {\n  module.exports = require('next/dist/server/route-modules/app-page/module.js')\n} else {\n  if (process.env.__NEXT_EXPERIMENTAL_REACT) {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.prod.js')\n      }\n    }\n  } else {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.prod.js')\n      }\n    }\n  }\n}\n"], "names": ["process", "env", "NEXT_RUNTIME", "module", "exports", "require", "__NEXT_EXPERIMENTAL_REACT", "NODE_ENV", "TURBOPACK"], "mappings": "AAAA,IAAIA,QAAQC,GAAG,CAACC,YAAY,KAAK,QAAQ;;KAElC;IACL,IAAIF,QAAQC,GAAG,CAACK,yBAAyB,EAAE;;SAcpC;QACL,IAAIN,QAAQC,GAAG,CAACM,QAAQ,KAAK,WAAe;YAC1C,IAAIP,QAAQC,GAAG,CAACO,SAAS,eAAE;gBACzBL,OAAOC,OAAO,GAAGC,QAAQ;YAC3B,OAAO;;QAGT,OAAO;;IAOT;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1115, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/AI/MetaRAG/metarag/frontend/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.ts"], "sourcesContent": ["module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['react-ssr']!.ReactJsxDevRuntime\n"], "names": ["module", "exports", "require", "vendored", "ReactJsxDevRuntime"], "mappings": "AAAAA,OAAOC,OAAO,GACZC,QAAQ,4HACRC,QAAQ,CAAC,YAAY,CAAEC,kBAAkB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1120, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/AI/MetaRAG/metarag/frontend/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react.ts"], "sourcesContent": ["module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['react-ssr']!.React\n"], "names": ["module", "exports", "require", "vendored", "React"], "mappings": "AAAAA,OAAOC,OAAO,GACZC,QAAQ,4HACRC,QAAQ,CAAC,YAAY,CAAEC,KAAK", "ignoreList": [0], "debugId": null}}]}