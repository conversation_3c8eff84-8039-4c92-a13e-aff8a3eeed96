.documentsSection {
  margin-top: 12px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  background-color: #f8f9fa;
  overflow: hidden;
}

.documentsHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background-color: #f0f2f5;
  cursor: pointer;
  user-select: none;
  transition: background-color 0.2s ease;
}

.documentsHeader:hover {
  background-color: #e8eaed;
}

.documentsTitle {
  font-weight: 500;
  color: #333;
  font-size: 14px;
}

.expandIcon {
  font-size: 12px;
  color: #666;
  transition: transform 0.2s ease;
}

.expandIcon.expanded {
  transform: rotate(180deg);
}

.documentsContent {
  padding: 0;
  max-height: 400px;
  overflow-y: auto;
}

.documentItem {
  padding: 16px;
  border-bottom: 1px solid #e0e0e0;
}

.documentItem:last-child {
  border-bottom: none;
}

.documentTitle {
  font-weight: 600;
  color: #1a73e8;
  margin-bottom: 8px;
  font-size: 14px;
  line-height: 1.4;
}

.documentContent {
  color: #333;
  font-size: 13px;
  line-height: 1.5;
  margin-bottom: 8px;
  white-space: pre-wrap;
  word-wrap: break-word;
}

.documentMetadata {
  margin-top: 12px;
  padding: 8px;
  background-color: #f5f5f5;
  border-radius: 4px;
  font-size: 11px;
}

.documentMetadata strong {
  color: #666;
  display: block;
  margin-bottom: 4px;
}

.documentMetadata pre {
  margin: 0;
  color: #555;
  font-family: 'Courier New', monospace;
  white-space: pre-wrap;
  word-wrap: break-word;
}

.noDocuments {
  padding: 16px;
  text-align: center;
  color: #666;
  font-style: italic;
  font-size: 14px;
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .documentsSection {
    border-color: #404040;
    background-color: #2a2a2a;
  }

  .documentsHeader {
    background-color: #333;
  }

  .documentsHeader:hover {
    background-color: #404040;
  }

  .documentsTitle {
    color: #e0e0e0;
  }

  .expandIcon {
    color: #ccc;
  }

  .documentItem {
    border-bottom-color: #404040;
  }

  .documentTitle {
    color: #4fc3f7;
  }

  .documentContent {
    color: #e0e0e0;
  }

  .documentMetadata {
    background-color: #333;
  }

  .documentMetadata strong {
    color: #ccc;
  }

  .documentMetadata pre {
    color: #ddd;
  }

  .noDocuments {
    color: #ccc;
  }
}
